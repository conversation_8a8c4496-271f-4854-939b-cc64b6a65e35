<?php
// index.php

ob_start();

// Include session configuration before starting session
include("session_config.php");
session_start();

include("../admin/inc/config.php");
include("../admin/inc/functions.php");
include("../admin/inc/CSRF_Protect.php");

// Initialize automatic cleanup system
include("auto_cleanup.php");

// ----------------------
// FETCH SETTINGS
// ----------------------
$statement = $pdo->prepare("SELECT * FROM tbl_settings WHERE id=1");
$statement->execute();
$settings = $statement->fetch(PDO::FETCH_ASSOC);
$footer_copyright = isset($settings['footer_copyright'])
    ? $settings['footer_copyright']
    : "© 2025 SMART LIFE. All rights reserved.";

// ----------------------
// FETCH SLIDER DATA
// ----------------------
$statement = $pdo->prepare("SELECT * FROM tbl_slider");
$statement->execute();
$sliders = $statement->fetchAll(PDO::FETCH_ASSOC);

// ----------------------
// FETCH FEATURED PRODUCTS (for "Signature Blends")
$statement = $pdo->prepare("SELECT * FROM tbl_product WHERE p_is_active=? AND p_is_featured=? LIMIT 3");
$statement->execute(array(1, 1));
$featured_products = $statement->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>SMART | Automation</title>
  <link rel="icon" type="image/png" href="../assets/uploads/logo.png">
  <!-- External CSS -->
  <link rel="stylesheet" href="css/style.css" />
  <!-- Tailwind CSS for Footer Component -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- Font Awesome for Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />

  <!-- Inline CSS for Hero Section, Slider Carousel, and Products Section -->
  <style>
    /* Toast Notification Styles */
    .toast-container {
      position: fixed;
      bottom: 20px;
      right: 20px;
      z-index: 9999;
    }

    /* Category Dropdown Styles */
    .dropdown-container {
      position: relative;
      display: inline-block;
    }

    .dropdown-toggle {
      display: flex;
      align-items: center;
      gap: 5px;
      color: var(--dark);
      text-decoration: none;
      font-weight: 500;
      font-size: 0.95rem;
      letter-spacing: 1px;
      position: relative;
      padding: 5px 0;
      transition: color 0.3s ease;
    }

    .dropdown-toggle::before {
      content: "";
      position: absolute;
      width: 100%;
      transform: scaleX(0);
      height: 2px;
      bottom: 0;
      left: 0;
      background-color: var(--secondary);
      transform-origin: bottom right;
      transition: transform 0.3s ease-out;
    }

    .dropdown-toggle:hover::before {
      transform: scaleX(1);
      transform-origin: bottom left;
    }

    .dropdown-icon {
      font-size: 0.7rem;
      transition: transform 0.3s ease;
    }

    .dropdown-container:hover .dropdown-icon {
      transform: rotate(180deg);
    }

    .dropdown-menu {
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%) translateY(10px);
      background-color: white;
      min-width: 250px;
      box-shadow: 0 10px 25px rgba(0,0,0,0.15);
      border-radius: 12px;
      padding: 25px 15px;
      z-index: 1000;
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease;
      border-top: 3px solid var(--secondary);
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      gap: 10px;
      width: auto;
    }

    .dropdown-column {
      display: flex;
      flex-direction: column;
      min-width: 220px;
      border-right: 1px solid #f0f0f0;
      padding: 0 20px;
      margin: 0 5px;
    }

    .dropdown-column:last-child {
      border-right: none;
    }

    .dropdown-column:first-child {
      padding-left: 10px;
    }

    .dropdown-container:hover .dropdown-menu {
      opacity: 1;
      visibility: visible;
      transform: translateX(-50%) translateY(0);
    }

    @media (max-width: 1200px) {
      .dropdown-menu {
        left: 0;
        transform: translateX(0) translateY(10px);
        max-width: 800px;
      }

      .dropdown-container:hover .dropdown-menu {
        transform: translateX(0) translateY(0);
      }
    }

    .dropdown-item {
      position: relative;
      padding: 12px 0;
      border-bottom: 1px solid #f5f5f5;
      width: 100%;
      transition: all 0.2s ease;
    }

    .dropdown-item:last-child {
      border-bottom: none;
    }

    .dropdown-item:hover {
      background-color: #f8f9fa;
      border-radius: 6px;
      transform: translateX(3px);
    }

    .category-link {
      color: var(--dark);
      text-decoration: none;
      display: block;
      font-weight: 500;
      transition: all 0.3s ease;
      position: relative;
      padding: 0 20px 0 10px;
      font-size: 0.95rem;
      letter-spacing: 0.3px;
    }

    .category-link:hover {
      color: var(--secondary);
    }

    /* Add arrow indicator for items with subcategories */
    .dropdown-item:has(.subcategory-menu) .category-link::after {
      content: "›";
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      font-size: 1.2rem;
      color: #ccc;
      transition: color 0.3s ease;
    }

    .dropdown-item:hover .category-link::after {
      color: var(--secondary);
    }

    /* Improved subcategory menu */
    .subcategory-menu {
      position: absolute;
      top: 0;
      left: 100%;
      background-color: white;
      min-width: 220px;
      box-shadow: 0 8px 16px rgba(0,0,0,0.1);
      border-radius: 8px;
      padding: 15px;
      opacity: 0;
      visibility: hidden;
      transform: translateX(10px);
      transition: all 0.3s ease;
      border-left: 3px solid var(--secondary);
      display: flex;
      flex-direction: column;
      gap: 8px;
      z-index: 1001;
    }

    .dropdown-item:hover .subcategory-menu {
      opacity: 1;
      visibility: visible;
      transform: translateX(0);
    }

    .subcategory-link {
      color: var(--dark);
      text-decoration: none;
      display: block;
      padding: 8px 12px;
      font-weight: 400;
      transition: all 0.3s ease;
      border-radius: 4px;
      position: relative;
      padding-left: 25px;
    }

    .subcategory-link::before {
      content: "•";
      position: absolute;
      left: 10px;
      color: var(--secondary);
      opacity: 0.7;
    }

    .subcategory-link:hover {
      color: var(--secondary);
      background-color: #f8f9fa;
    }

    /* Submenu styles */
    .submenu {
      position: absolute;
      top: 0;
      left: 100%;
      background-color: white;
      min-width: 200px;
      box-shadow: 0 8px 16px rgba(0,0,0,0.1);
      border-radius: 8px;
      padding: 10px 0;
      opacity: 0;
      visibility: hidden;
      transform: translateX(10px);
      transition: all 0.3s ease;
      z-index: 1001;
      margin-left: 5px;
    }

    .has-submenu:hover .submenu {
      opacity: 1;
      visibility: visible;
      transform: translateX(0);
    }

    .submenu-item {
      display: block;
      padding: 8px 20px;
      color: #333;
      text-decoration: none;
      transition: all 0.2s ease;
      border-bottom: 1px solid #f0f0f0;
    }

    .submenu-item:last-child {
      border-bottom: none;
    }

    .submenu-item:hover {
      background-color: #f8f9fa;
      color: var(--secondary);
    }

    .submenu-arrow {
      font-size: 1.2em;
      color: #ccc;
      transition: color 0.3s ease;
    }

    .has-submenu:hover .submenu-arrow {
      color: var(--secondary);
    }

    /* Ensure proper spacing and alignment */
    .dropdown-item {
      display: flex;
      align-items: center;
    }

    .category-link {
      flex: 1;
    }

    /* Add subtle animation for submenu */
    @keyframes fadeInRight {
      from {
        opacity: 0;
        transform: translateX(10px);
      }
      to {
        opacity: 1;
        transform: translateX(0);
      }
    }

    .has-submenu:hover .submenu {
      animation: fadeInRight 0.3s ease forwards;
    }

    .toast {
      background: white;
      border-radius: 8px;
      padding: 16px;
      margin-top: 10px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      display: flex;
      align-items: center;
      min-width: 300px;
      transform: translateX(120%);
      transition: transform 0.3s ease-in-out;
    }

    .toast.show {
      transform: translateX(0);
    }

    .toast.success {
      border-left: 4px solid #10B981;
    }

    .toast.error {
      border-left: 4px solid #EF4444;
    }

    .toast-icon {
      width: 24px;
      height: 24px;
      margin-right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .toast.success .toast-icon {
      color: #10B981;
    }

    .toast.error .toast-icon {
      color: #EF4444;
    }

    .toast-content {
      flex: 1;
    }

    .toast-title {
      font-weight: 600;
      margin-bottom: 4px;
      color: #1F2937;
    }

    .toast-message {
      color: #6B7280;
      font-size: 0.875rem;
    }

    .toast-close {
      background: none;
      border: none;
      color: #9CA3AF;
      cursor: pointer;
      padding: 4px;
      margin-left: 12px;
    }

    .toast-close:hover {
      color: #4B5563;
    }

    /* --- Hero Section & Slider Carousel Styles --- */
    .hero {
      height: 70vh;
      display: flex;
      align-items: center;
      background-color: var(--light);
      position: relative;
      overflow: hidden;
    }
    .hero::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: radial-gradient(circle at center, rgba(255,255,255,0.3) 0%, rgba(248,249,250,0.9) 100%);
      z-index: 1;
    }
    .hero-video {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      opacity: 0.6;
    }
    .hero-content {
      position: relative;
      z-index: 2;
      max-width: 1700px;
    }
    .item {
      opacity: 0;
      transition: opacity 0.8s ease-in-out;
      position: absolute;
      width: 100%;
      height: 100%;
    }
    .item.active {
      opacity: 1;
      z-index: 1;
    }
    #bootstrap-touch-slider {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
    }
    #bootstrap-touch-slider .carousel-inner,
    #bootstrap-touch-slider .item {
      width: 100%;
      height: 100%;
    }
    #bootstrap-touch-slider .item {
      background-size: cover;
      background-position: center center;
      opacity: 0;
      transition: opacity 1s ease-in-out;
    }
    #bootstrap-touch-slider .item.active {
      position: relative;
      opacity: 1;
    }
    #bootstrap-touch-slider .carousel-indicators {
      position: absolute;
      bottom: 15px;
      left: 50%;
      transform: translateX(-50%);
      list-style: none;
      margin: 0;
      padding: 0;
      z-index: 100;
    }
    #bootstrap-touch-slider .carousel-indicators li {
      display: inline-block;
      width: 12px;
      height: 12px;
      margin: 0 5px;
      background-color: #fff;
      border-radius: 50%;
      cursor: pointer;
    }
    #bootstrap-touch-slider .carousel-indicators .active {
      background-color: #000;
    }
    .slide-text {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 2;
      color: #fff;
      text-align: center;
      width: 80%;
    }
    .slide_style_left { text-align: left; left: 10%; transform: translateY(-50%); }
    .slide_style_center { text-align: center; }
    .slide_style_right { text-align: right; right: 10%; left: auto; transform: translateY(-50%); }
    .hero .carousel, .hero .carousel-inner { height: 100%; }



    /* --- Products Section Styles --- */
    .category-section {
      margin-bottom: 60px;
    }
    .products-grid {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-bottom: 20px;
      justify-content: flex-start;
    }
    .product-card {
      flex: 0 0 calc(33.333% - 20px);  /* Three items per row */
      max-width: calc(33.333% - 20px); /* Prevent expanding beyond 1/3 width */
      min-width: 250px; /* Minimum width for small screens */
      background: #fff;
      border: 1px solid #eee;
      padding: 15px;
      text-align: center;
      box-shadow: var(--shadow);
      transition: transform 0.3s;
      box-sizing: border-box;
      display: block;
      margin-bottom: 20px;
    }
    .product-card:hover {
      transform: translateY(-5px);
    }
    .product-img img {
      max-width: 100%;
      height: auto;
      margin-bottom: 10px;
    }
    .product-title {
      font-size: 1.2rem;
      margin-bottom: 5px;
    }
    .product-price {
      font-size: 1rem;
      margin-bottom: 10px;
      color: var(--secondary);
    }
    .product-card p {
      font-size: 0.9rem;
      margin-bottom: 10px;
    }
    .add-to-cart {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      background: linear-gradient(to right, #2563eb, #3b82f6);
      color: white;
      font-size: 0.875rem;
      font-weight: 500;
      padding: 0.75rem 1rem;
      border-radius: 0.5rem;
      border: none;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .add-to-cart:hover {
      background: linear-gradient(to right, #1d4ed8, #2563eb);
      transform: translateY(-2px);
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .add-to-cart:disabled {
      background: #e5e7eb;
      color: #6b7280;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
      opacity: 0.8;
    }
    .add-to-cart svg {
      width: 1.25rem;
      height: 1.25rem;
    }
    .add-to-cart.added {
      background: #10b981;
      color: white;
    }
    .add-to-cart.added:hover {
      background: #059669;
    }
    .add-to-cart.added:disabled {
      background: #10b981;
      color: white;
      opacity: 0.8;
    }
    .view-more {
      text-align: center;
    }
    .btn.view-more-btn {
      background-color: var(--secondary);
      color: var(--primary);
      padding: 10px 20px;
      text-decoration: none;
      border-radius: 4px;
      display: inline-block;
      transition: background-color 0.3s;
    }
    .btn.view-more-btn:hover {
      background-color: var(--accent);
    }

    /* --- Responsive Styling (Vertical arrangement on small screens) --- */
    @media (max-width: 992px) {
      .product-card {
        flex: 0 0 calc(50% - 20px);
        max-width: calc(50% - 20px);
      }
    }
    @media (max-width: 768px) {
      .products-grid {
        justify-content: center;
      }
      .product-card {
        flex: 0 0 100%;
        max-width: 320px;
      }
      .add-to-cart {
        text-decoration: none !important;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    @media (max-width: 768px) { /* Target tablets and phones */
    .hero {
        height: 60vh; /* Reduce overall height slightly */
        /* Maybe force content alignment if needed */
        /* display: flex; */
        /* flex-direction: column; */
        /* justify-content: center; */
    }

    /* Adjust the static content that appears above the slider */
    .hero-content {
        padding: 0 8px; /* Add some horizontal padding */
        /* Reduce text sizes */
    }
    .hero-title {
    font-size: 2rem; /* Decrease font size */
    line-height: 1.5;
    margin-bottom: 10px;
    text-align: left; /* Force left alignment */
    margin-left: 0; /* Remove any left margin */
    padding-left: 0.8%; /* Add padding from left edge */
    width: 100%; /* Take full width of container */
    max-width: 90%; /* Prevent stretching too far right */
}

.hero-subtitle {
    font-size: 1.3rem; /* Decrease font size */
    margin-bottom: 20px;
    max-width: 50%; /* Prevent it being too wide */
    margin-left: 5%; /* Match title's left alignment */
    margin-right: auto;
    text-align: left; /* Force left alignment */
    padding-left: 0.1; /* Remove any padding */
}
    .hero-btn {
        padding: 8px 16px; /* Slightly smaller button */
        font-size: 0.9rem;
    }

    /* Adjust the text *within* the slider items */
    .slide-text {
        width: 90%; /* Allow text block to use more width */
        padding: 0 10px; /* Add internal padding */
        /* Adjust positioning if needed, e.g., move slightly down */
        /* top: 55%; */
    }

    /* Reduce font sizes for text inside the slider (if you uncomment it) */
    .slide-text h1 {
        font-size: 1.8rem; /* Decrease font size */
        margin-bottom: 8px;
    }
    .slide-text p {
        font-size: 0.85rem; /* Decrease font size */
        /* Optionally hide on very small screens if still too cluttered */
        /* display: none; */
    }
     .slide-text .btn { /* If slider has buttons */
        padding: 6px 12px;
        font-size: 0.8rem;
     }


    /* Center alignment might work better on mobile for all positions */
    .slide_style_left,
    .slide_style_right {
        text-align: center; /* Force center */
        left: 50%; /* Re-center */
        transform: translate(-50%, -50%); /* Standard centering transform */
        right: auto; /* Reset right positioning */
    }

    /* Optional: Make controls less intrusive */
     .carousel-control .fa {
        font-size: 24px; /* Smaller arrows */
     }
     .carousel-indicators {
         bottom: 10px; /* Adjust position */
     }
     .carousel-indicators li {
         width: 10px;
         height: 10px;
     }
}

@media (max-width: 480px) { /* Even smaller screens */
    .hero {
        height: 55vh; /* Further reduce height */
    }
     .hero-title {
        font-size: 1.7rem;
        text-align: left; /* Force left alignment */
     }
     .hero-subtitle {
        font-size: 4.8rem;
        text-align: left; /* Force left alignment */
        /* display: none; /* Option to hide subtitle entirely */
     }
     .slide-text h1 {
         font-size: 1.5rem;
     }
     .slide-text p {
         font-size: 0.8rem;
         /* display: none; /* Hide slider text paragraph */
     }
}


.hero-title {
  font-size: 3rem;          /* ~48px */
  margin: 0.5em 0 0.75em;    /* tighten or loosen as desired */
  line-height: 1.2;
  font-weight: 700;
  color: #2d3748;
}

.hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: 1.5em;
  line-height: 1.4;
  color: #4a5568;
  max-width: 600px;
}

/* Responsive adjustments for hero text */
@media (max-width: 1024px) {
  .hero-title {
    font-size: 2.5rem;
    margin: 0.4em 0 0.6em;
    text-align: left; /* Force left alignment */
  }

  .hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 1.2em;
    text-align: left; /* Force left alignment */
  }
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2rem;
    margin: 0.3em 0 0.5em;
    text-align: left; /* Force left alignment */
  }

  .hero-subtitle {
    font-size: 1.1rem;
    margin-bottom: 1em;
    text-align: left; /* Force left alignment */
    max-width: 100%;
    padding: 0 20px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 1.75rem;
    margin: 0.2em 0 0.4em;
    padding: 0 15px;
    text-align: left; /* Force left alignment */
  }

  .hero-subtitle {
    font-size: 1rem;
    margin-bottom: 0.8em;
    padding: 0 5px;
    text-align: left; /* Force left alignment */
  }
}

/* 3. Section header titles (e.g. .section-title) */
.section-title {
  font-size: 2rem;          /* ~32px */
  margin: 1em 0;            /* space above & below each section */
}
.subcategory-filter {
  display: flex;
  gap: 12px;
  margin: 20px 0;
  flex-wrap: wrap;
  padding: 5px;
  background: #f8f9fa;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.subcategory-btn {
  padding: 10px 20px;
  border: none;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: 0.95rem;
  color: #4a5568;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  position: relative;
  z-index: 1;
}

.subcategory-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--secondary) 0%, var(--primary) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
  border-radius: 8px;
}

.subcategory-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  color: var(--secondary);
}

.subcategory-btn.active {
  color: white;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.subcategory-btn.active::before {
  opacity: 1;
}

.subcategory-btn.active::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    linear-gradient(45deg, transparent 45%, rgba(255,255,255,0.1) 50%, transparent 55%);
  background-size: 200% 200%;
  animation: shine 3s infinite;
  z-index: -1;
  border-radius: 8px;
}

.subcategory-btn.active:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0,0,0,0.15);
  color: white;
}

@keyframes shine {
  0% {
    background-position: 200% 200%;
  }
  100% {
    background-position: -200% -200%;
  }
}

@media (max-width: 768px) {
  .subcategory-filter {
    gap: 8px;
    padding: 4px;
  }

  .subcategory-btn {
    padding: 8px 16px;
    font-size: 0.9rem;
  }
}

.product-card {
  display: block;
  transition: all 0.3s ease;
}

.product-card.hidden {
  display: none !important;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

    /* Other existing CSS (About, Gallery, Contact, Footer) remains unchanged */

    /* Search Styles */
    .search-container {
      position: relative;
      margin: 0 15px;
    }

    .search-box {
      display: flex;
      align-items: center;
      background: white;
      border-radius: 8px;
      padding: 5px 10px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
      border: 1px solid #e2e8f0;
      transition: all 0.3s ease;
    }

    .search-box:focus-within {
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      border-color: var(--secondary);
    }

    #searchInput {
      border: none;
      outline: none;
      padding: 8px;
      width: 200px;
      font-size: 0.95rem;
      background: transparent;
    }

    .search-btn {
      background: none;
      border: none;
      padding: 5px;
      cursor: pointer;
      color: #4a5568;
      transition: color 0.3s ease;
    }

    .search-btn:hover {
      color: var(--secondary);
    }

    .search-suggestions {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: white;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      margin-top: 5px;
      max-height: 300px;
      overflow-y: auto;
      display: none;
      z-index: 1000;
    }

    .search-suggestions.active {
      display: block;
    }

    .suggestion-item {
      padding: 10px 15px;
      cursor: pointer;
      display: flex;
      align-items: center;
      transition: background-color 0.2s ease;
    }

    .suggestion-item:hover {
      background-color: #f8f9fa;
    }

    .suggestion-item .icon {
      margin-right: 10px;
      color: var(--secondary);
    }

    .suggestion-item .type {
      font-size: 0.8rem;
      color: #718096;
      margin-left: auto;
    }

    .suggestion-item .highlight {
      background-color: #fff3cd;
      padding: 0 2px;
      border-radius: 2px;
    }

    @media (max-width: 768px) {
      .search-container {
        margin: 10px 0;
        width: 100%;
      }

      .search-box {
        width: 100%;
      }

      #searchInput {
        width: 100%;
      }
    }

    .deal-card {
      position: relative;
      background: #fff;
      border: 1px solid #eee;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: var(--shadow);
      transition: transform 0.3s;
    }

    .deal-card:hover {
      transform: translateY(-5px);
    }

    .deal-card .discount-badge {
      position: absolute;
      top: 10px;
      right: 10px;
      background: #ff4444;
      color: white;
      padding: 5px 10px;
      border-radius: 4px;
      font-size: 0.8rem;
      font-weight: bold;
      z-index: 1;
    }

    .deal-img {
      position: relative;
      padding-top: 75%;
      overflow: hidden;
    }

    .deal-img img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }

    .deal-card:hover .deal-img img {
      transform: scale(1.05);
    }

    .deal-content {
      padding: 15px;
    }

    .deal-title {
      margin: 0 0 10px 0;
      font-size: 1.1rem;
      color: #333;
    }

    .price-container {
      margin-bottom: 10px;
    }

    .old-price {
      text-decoration: line-through;
      color: #999;
      margin-right: 10px;
      font-size: 0.9rem;
    }

    .current-price {
      color: #ff4444;
      font-weight: bold;
      font-size: 1.1rem;
    }

    .deal-description {
      color: #666;
      font-size: 0.9rem;
      margin-bottom: 15px;
      line-height: 1.4;
    }

    .deal-card .add-to-cart {
      width: 100%;
      padding: 8px;
      background: #ff4444;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.3s;
      font-weight: 500;
    }

    .deal-card .add-to-cart:hover {
      background: #ff6666;
    }

    #message-container {
        margin-bottom: 15px;
    }

    .alert {
        padding: 12px;
        border-radius: 4px;
        font-size: 14px;
    }

    .alert-success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .alert-danger {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    #newsletter-message {
        margin-top: 10px;
    }

    .newsletter-form {
        display: flex;
        gap: 10px;
    }

    .newsletter-input {
        flex: 1;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
    }

    .newsletter-btn {
        padding: 8px 12px;
        background: var(--secondary);
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        transition: background 0.3s;
    }

    .newsletter-btn:hover {
        background: var(--accent);
    }

    .newsletter-btn:disabled {
        background: #ccc;
        cursor: not-allowed;
    }

    .cart-icon {
      position: relative;
      margin-left: 15px;
    }

    .cart-count {
      position: absolute;
      top: -8px;
      right: -8px;
      background-color: #007bff;
      color: white;
      border-radius: 50%;
      padding: 2px 6px;
      font-size: 12px;
      font-weight: bold;
      min-width: 18px;
      text-align: center;
    }

    /* Add these styles to your existing CSS */
    .dropdown-menu.active {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }

    .dropdown-toggle.active .dropdown-icon {
      transform: rotate(180deg);
    }

    .subcategory-menu.active {
      opacity: 1;
      visibility: visible;
      transform: translateX(0);
    }

    .category-link.active {
      color: var(--secondary);
    }

    /* Add touch-friendly styles */
    @media (hover: none) {
      .dropdown-container:hover .dropdown-menu,
      .dropdown-item:hover .subcategory-menu {
        opacity: 0;
        visibility: hidden;
        transform: translateY(10px);
      }

      .dropdown-menu.active,
      .subcategory-menu.active {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
      }
    }

    /* Add styles for clickable category links */
    .category-link {
      cursor: pointer;
      position: relative;
      padding-right: 25px; /* Make room for the arrow */
    }

    .category-link::after {
      content: "›";
      position: absolute;
      right: 5px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 1.2em;
      color: #666;
      transition: transform 0.3s ease;
    }

    .category-link.active::after {
      transform: translateY(-50%) rotate(90deg);
    }

    /* Add these styles to your existing CSS */
    @media (max-width: 768px) {
      .nav-links {
        align-items: flex-start;
        overflow-y: auto;
        height: 100vh;
        background: white;
      }

      .nav-links a {
        text-align: left;
        width: 100%;
        padding: 12px 15px;
        border-bottom: 1px solid #eee;
      }

      .dropdown-container {
        width: 100%;
      }

      .dropdown-toggle {
        justify-content: space-between;
        text-align: left;
        padding: 12px 15px;
        border-bottom: 1px solid #eee;
      }

      .dropdown-menu {
        display: none;
        position: static;
        width: 100%;
        background: #f8f9fa;
        padding: 0;
        margin: 0;
        box-shadow: none;
        border: none;
        flex-direction: column;
        transform: none !important;
        left: 0;
      }

      .dropdown-column {
        width: 100%;
        border-right: none;
        padding-right: 0;
      }

      .dropdown-menu.active {
        display: flex;
      }

      .category-link {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px 15px;
        border-bottom: 1px solid #eee;
      }

      .subcategory-menu {
        display: none;
        position: static;
        width: 100%;
        background: #f8f9fa;
        padding: 0;
        margin: 0;
        border-left: 2px solid var(--secondary);
      }

      .subcategory-menu.active {
        display: block;
      }

      .subcategory-link {
        padding: 10px 15px 10px 25px;
        border-bottom: 1px solid #eee;
      }

      .dropdown-item {
        position: relative;
        display: flex;
        flex-direction: column;
        width: 100%;
        min-width: 100%;
        padding: 0;
        border-bottom: 1px solid #eee;
      }

      /* Search container in mobile */
      .search-container {
        padding: 10px 15px;
        border-bottom: 1px solid #eee;
      }

      .search-box {
        width: 100%;
      }

      /* Cart icon in mobile */
      .cart-icon {
        padding: 10px 15px;
        border-bottom: 1px solid #eee;
      }
    }

    /* Footer Component Override Styles */
    /* Override existing footer styles to ensure our Tailwind footer component displays correctly */
    footer.bg-gradient-to-br {
      background: linear-gradient(to bottom right, #111827, #1f2937, #111827) !important;
      padding: 0 !important;
      color: inherit !important;
    }

    footer.bg-gradient-to-br * {
      color: inherit !important;
    }

    footer.bg-gradient-to-br .container {
      max-width: 1200px !important;
      margin: 0 auto !important;
      padding: 0 1rem !important;
    }

    /* Ensure Tailwind classes work properly */
    footer.bg-gradient-to-br .grid {
      display: grid !important;
    }

    footer.bg-gradient-to-br .flex {
      display: flex !important;
    }

    /* Reset any conflicting styles */
    footer.bg-gradient-to-br .footer-top,
    footer.bg-gradient-to-br .footer-col,
    footer.bg-gradient-to-br .footer-links,
    footer.bg-gradient-to-br .footer-bottom {
      all: unset !important;
    }
  </style>
</head>
<body>
  <!-- Header Section -->
  <header>
    <div class="container">
      <nav>
        <a href="#" class="logo">SMART LIFE<span>.</span></a>
        <div class="nav-links">
          <a href="#home">Home</a>
          <a href="#about">About</a>
          <!-- Products Dropdown -->
          <div class="dropdown-container">
            <a class="dropdown-toggle">Category <i class="dropdown-icon">▼</i></a>
            <div class="dropdown-menu">
              <?php
              // Fetch all top-level categories that are to be shown on the menu
              $stmt_header_cat = $pdo->prepare("SELECT * FROM tbl_top_category WHERE show_on_menu = 1 ORDER BY tcat_name ASC");
              $stmt_header_cat->execute();
              $header_categories = $stmt_header_cat->fetchAll(PDO::FETCH_ASSOC);

              // Calculate total number of categories
              $total_categories = count($header_categories);

              // Calculate how many columns we need (5 items per column)
              $columns_needed = ceil($total_categories / 5);

              // Create column divs
              for($col = 0; $col < $columns_needed; $col++):
              ?>
                <div class="dropdown-column">
                  <?php
                  // Calculate start and end index for this column
                  $start_index = $col * 5;
                  $end_index = min(($col + 1) * 5, $total_categories);

                  // Loop through categories for this column
                  for($i = $start_index; $i < $end_index; $i++):
                    $header_cat = $header_categories[$i];

                    // Fetch subcategories for this category
                    $stmt_header_subcat = $pdo->prepare("SELECT * FROM tbl_mid_category WHERE tcat_id = ? ORDER BY mcat_name ASC");
                    $stmt_header_subcat->execute(array($header_cat['tcat_id']));
                    $header_subcategories = $stmt_header_subcat->fetchAll(PDO::FETCH_ASSOC);
                  ?>
                    <div class="dropdown-item">
                      <a href="category.php?id=<?php echo $header_cat['tcat_id']; ?>" class="category-link"><?php echo htmlspecialchars($header_cat['tcat_name']); ?></a>
                      <?php if(!empty($header_subcategories)): ?>
                        <div class="subcategory-menu">
                          <?php foreach($header_subcategories as $header_subcat): ?>
                            <a href="category.php?id=<?php echo $header_cat['tcat_id']; ?>&subcat=<?php echo $header_subcat['mcat_id']; ?>" class="subcategory-link"><?php echo htmlspecialchars($header_subcat['mcat_name']); ?></a>
                          <?php endforeach; ?>
                        </div>
                      <?php endif; ?>
                    </div>
                  <?php endfor; ?>
                </div>
              <?php endfor; ?>
            </div>
          </div>
          <a href="#gallery">Best Deals</a>
          <a href="all_products.php">All Products</a>
          <a href="#contact">Contact</a>
          <!-- Search Bar -->
          <div class="search-container">
            <div class="search-box">
              <input type="text" id="searchInput" placeholder="Search products, categories..." autocomplete="off">
              <button type="button" class="search-btn">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z"/>
                </svg>
              </button>
            </div>
            <div class="search-suggestions" id="searchSuggestions"></div>
          </div>
          <!-- Cart Button -->
          <div class="cart-icon">
            <a href="cart.php">
              🛒 <span class="cart-count">0</span>
            </a>
          </div>
        </div>
        <div class="mobile-menu-trigger">
          <div class="menu-icon">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </nav>
    </div>
  </header>

  <!-- New Mobile Menu -->
  <div class="mobile-menu">
    <div class="mobile-menu-header">
      <div class="mobile-logo">SMART LIFE<span>.</span></div>
      <div class="mobile-close">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M18 6L6 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
    </div>

    <div class="mobile-search">
      <input type="text" placeholder="Search products, categories..." id="mobileSearchInput" autocomplete="off">
      <button type="button" class="mobile-search-btn" id="mobileSearchButton">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M11 19C15.4183 19 19 15.4183 19 11C19 6.58172 15.4183 3 11 3C6.58172 3 3 6.58172 3 11C3 15.4183 6.58172 19 11 19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M21 21L16.65 16.65" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
      <div class="mobile-search-suggestions" id="mobileSearchSuggestions"></div>
    </div>

    <div class="mobile-nav">
      <a href="#home" class="mobile-nav-item"><span style="margin-right:8px;vertical-align:middle;"> <svg width="20" height="20" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path d="M3 12L12 3l9 9"/><path d="M9 21V9h6v12"/></svg> </span>Home</a>
      <a href="#about" class="mobile-nav-item"><span style="margin-right:8px;vertical-align:middle;"> <svg width="20" height="20" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10"/><path d="M12 16v-4"/><path d="M12 8h.01"/></svg> </span>About</a>
      <!-- Categories Accordion -->
      <div class="mobile-category-wrapper">
        <div class="mobile-category-header">
          <span><span style="margin-right:8px;vertical-align:middle;"><svg width="20" height="20" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path d="M3 7V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v2"/><path d="M3 7v12a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V7"/><path d="M16 3v4"/><path d="M8 3v4"/></svg></span>Categories</span>
          <svg class="mobile-category-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <div class="mobile-category-content">
          <?php
          // Fetch all top-level categories that are to be shown on the menu
          $stmt_mobile_cat = $pdo->prepare("SELECT * FROM tbl_top_category WHERE show_on_menu = 1 ORDER BY tcat_name ASC");
          $stmt_mobile_cat->execute();
          $mobile_categories = $stmt_mobile_cat->fetchAll(PDO::FETCH_ASSOC);
          foreach($mobile_categories as $mobile_cat):
            // Fetch subcategories for this category
            $stmt_mobile_subcat = $pdo->prepare("SELECT * FROM tbl_mid_category WHERE tcat_id = ? ORDER BY mcat_name ASC");
            $stmt_mobile_subcat->execute(array($mobile_cat['tcat_id']));
            $mobile_subcategories = $stmt_mobile_subcat->fetchAll(PDO::FETCH_ASSOC);
            // Pick an SVG icon for each category
            $cat_svg = '<svg width="18" height="18" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10"/></svg>';
            if (stripos($mobile_cat['tcat_name'], 'switch') !== false) $cat_svg = '<svg width="18" height="18" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><rect x="6" y="6" width="12" height="12" rx="2"/><path d="M9 9h6v6H9z"/></svg>';
            // Pick an icon for each category (for demo, use emoji, can replace with SVG)
            $cat_icon = '🔌';
            if (stripos($mobile_cat['tcat_name'], 'switch') !== false) $cat_icon = '🎚️';
            if (stripos($mobile_cat['tcat_name'], 'sensor') !== false) $cat_icon = '🛰️';
            if (stripos($mobile_cat['tcat_name'], 'camera') !== false) $cat_icon = '📷';
            if (stripos($mobile_cat['tcat_name'], 'lock') !== false) $cat_icon = '🔒';
            if (stripos($mobile_cat['tcat_name'], 'light') !== false) $cat_icon = '💡';
          ?>
            <div class="mobile-category-item">
              <div class="mobile-category-item-header">
                <a href="category.php?id=<?php echo $mobile_cat['tcat_id']; ?>"> <span style="margin-right:8px;"><?php echo $cat_icon; ?></span><?php echo htmlspecialchars($mobile_cat['tcat_name']); ?></a>
                <?php if(!empty($mobile_subcategories)): ?>
                <svg class="mobile-subcategory-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <?php endif; ?>
              </div>
              <?php if(!empty($mobile_subcategories)): ?>
              <div class="mobile-subcategory-content">
                <?php foreach($mobile_subcategories as $mobile_subcat): ?>
                <a href="category.php?id=<?php echo $mobile_cat['tcat_id']; ?>&subcat=<?php echo $mobile_subcat['mcat_id']; ?>" class="mobile-subcategory-item"><span style="margin-right:8px;">🔸</span><?php echo htmlspecialchars($mobile_subcat['mcat_name']); ?></a>
                <?php endforeach; ?>
              </div>
              <?php endif; ?>
            </div>
          <?php endforeach; ?>
        </div>
      </div>
      <a href="#gallery" class="mobile-nav-item"><span style="margin-right:8px;">🔥</span>Best Deals</a>
      <a href="all_products.php" class="mobile-nav-item"><span style="margin-right:8px;">🛒</span>All Products</a>
      <a href="#contact" class="mobile-nav-item"><span style="margin-right:8px;">✉️</span>Contact</a>
    </div>

    <div class="mobile-cart">
      <a href="cart.php" class="mobile-cart-btn">
        <div class="mobile-cart-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 22C9.55228 22 10 21.5523 10 21C10 20.4477 9.55228 20 9 20C8.44772 20 8 20.4477 8 21C8 21.5523 8.44772 22 9 22Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M20 22C20.5523 22 21 21.5523 21 21C21 20.4477 20.5523 20 20 20C19.4477 20 19 20.4477 19 21C19 21.5523 19.4477 22 20 22Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M1 1H5L7.68 14.39C7.77144 14.8504 8.02191 15.264 8.38755 15.5583C8.75318 15.8526 9.2107 16.009 9.68 16H19.4C19.8693 16.009 20.3268 15.8526 20.6925 15.5583C21.0581 15.264 21.3086 14.8504 21.4 14.39L23 6H6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <span class="mobile-cart-text">View Cart</span>
        <span class="mobile-cart-count cart-count">0</span>
      </a>
    </div>
  </div>

  <!-- Mobile Menu Overlay -->
  <div class="mobile-overlay"></div>

  <!-- Hero Section -->
  <section class="hero" id="home">
    <div class="container hero-content">
      <h1 class="hero-title"> Technology | Class  <span>| Convenience</span></h1>
      <p class="hero-subtitle">Your Gateway to a Smarter, Luxurious Home</p>
      <a href="#products" class="btn hero-btn">SHOP NOW <span class="btn-icon">→</span></a>
    </div>
    <div class="left-gradient"></div>
    <div id="bootstrap-touch-slider" class="carousel bs-slider fade control-round indicators-line" data-ride="carousel" data-pause="hover" data-interval="false">
      <!-- Indicators -->
      <ol class="carousel-indicators">
        <?php
        $i = 0;
        foreach ($sliders as $row):
        ?>
          <li data-target="#bootstrap-touch-slider" data-slide-to="<?php echo $i; ?>" <?php if ($i == 0) echo 'class="active"'; ?>></li>
        <?php
          $i++;
        endforeach;
        ?>
      </ol>
      <!-- Slides Wrapper -->
      <div class="carousel-inner" role="listbox">
        <?php
        $i = 0;
        foreach ($sliders as $row):
          $photo = !empty($row['photo']) ? "../assets/uploads/" . $row['photo'] : "./images/hero.jpg";
        ?>
          <div class="item <?php if ($i == 0) echo 'active'; ?>" style="background-image: url('<?php echo $photo; ?>');">
            <div class="bs-slider-overlay"></div>
            <div class="container">
              <div class="row">
                <div class="slide-text <?php
                  if ($row['position'] == 'Left') {
                      echo 'slide_style_left';
                  } elseif ($row['position'] == 'Center') {
                      echo 'slide_style_center';
                  } elseif ($row['position'] == 'Right') {
                      echo 'slide_style_right';
                  }
                ?>">
                  <h1 data-animation="animated <?php
                    if ($row['position'] == 'Left') {
                        echo 'zoomInLeft';
                    } elseif ($row['position'] == 'Center') {
                        echo 'flipInX';
                    } elseif ($row['position'] == 'Right') {
                        echo 'zoomInRight';
                    }
                  ?>"></h1>
                  <p data-animation="animated <?php
                    if ($row['position'] == 'Left') {
                        echo 'fadeInLeft';
                    } elseif ($row['position'] == 'Center') {
                        echo 'fadeInDown';
                    } elseif ($row['position'] == 'Right') {
                        echo 'fadeInRight';
                    }
                  ?>"></p>
                </div>
              </div>
            </div>
          </div>
        <?php
          $i++;
        endforeach;
        ?>
      </div>
      <!-- Slider Controls -->
      <a class="left carousel-control" href="#bootstrap-touch-slider" role="button" data-slide="prev">
        <span class="fa fa-angle-left" aria-hidden="true"></span>
        <span class="sr-only">Previous</span>
      </a>
      <a class="right carousel-control" href="#bootstrap-touch-slider" role="button" data-slide="next">
        <span class="fa fa-angle-right" aria-hidden="true"></span>
        <span class="sr-only">Next</span>
      </a>
    </div>
  </section>

  <!-- About Section -->
<section class="about" id="about">
  <div class="container">
    <div class="section-header">
      <h2 class="section-title">Your Gateway to Intelligent Luxury</h2>
    </div>
    <div class="about-container">
      <div class="about-img">
        <img src="../admin/img/img1.jpg" alt="Luxury Smart Home System" />
      </div>
      <div class="about-content">
        <h3>Where Technology Meets Elegance</h3>
        <p>
          We curate the world's most sophisticated smart home systems for those who demand both cutting-edge innovation and
          impeccable design. Each device is meticulously engineered to deliver uncompromising performance while elevating
          your living space with sleek, minimalist aesthetics.
        </p>
        <p>
          Our exclusive partnerships with award-winning designers and tech innovators allow us to offer you bespoke home
          automation solutions that blend invisibly into your luxury environment while delivering extraordinary convenience.
        </p>
        <div class="features">
          <div class="feature">
            <div class="feature-icon">🏆</div>
            <div class="feature-content">
              <h4>Designer-Grade Hardware</h4>
              <p>Brushed metal and tempered glass finishes that complement high-end interiors</p>
            </div>
          </div>
          <div class="feature">
            <div class="feature-icon">🎚️</div>
            <div class="feature-content">
              <h4>Concierge Automation</h4>
              <p>AI that learns your preferences to create perfect lighting, climate and ambiance</p>
            </div>
          </div>
          <div class="feature">
            <div class="feature-icon">🤵</div>
            <div class="feature-content">
              <h4>White-Glove Service</h4>
              <p>Dedicated smart home specialists for personalized system design and installation</p>
            </div>
          </div>
          <div class="feature">
            <div class="feature-icon">🔐</div>
            <div class="feature-content">
              <h4>Discreet Security</h4>
              <p>Enterprise-grade protection hidden behind elegant interfaces</p>
            </div>
          </div>
        </div>
        <a href="#contact" class="cta-button">Schedule Your Private Consultation</a>
      </div>
    </div>
  </div>
</section>

 <!-- Dynamically Generated Products Section -->
<section class="products" id="products">
  <div class="container">
    <?php
    // Fetch all top-level categories that are to be shown on the menu.
    $stmt_cat = $pdo->prepare("SELECT * FROM tbl_top_category WHERE show_on_menu = 1 ORDER BY
      CASE
        WHEN tcat_name = 'SMART SWITCHES' THEN 1
        ELSE 2
      END, tcat_name ASC");
    $stmt_cat->execute();
    $categories = $stmt_cat->fetchAll(PDO::FETCH_ASSOC);

    // Loop through each category
    foreach($categories as $category):
      // Fetch sub-categories for this top-level category
      $stmt_subcat = $pdo->prepare("SELECT * FROM tbl_mid_category WHERE tcat_id = ? ORDER BY mcat_name ASC");
      $stmt_subcat->execute(array($category['tcat_id']));
      $subcategories = $stmt_subcat->fetchAll(PDO::FETCH_ASSOC);

      // Check if category has any active products
      $stmt_check_products = $pdo->prepare("SELECT COUNT(*) as total FROM tbl_product WHERE tcat_id = ? AND p_is_active = 1");
      $stmt_check_products->execute(array($category['tcat_id']));
      $has_products = $stmt_check_products->fetch(PDO::FETCH_ASSOC)['total'] > 0;

      // Only show category if it has products
      if($has_products):
    ?>
    <div class="category-section" data-category-id="<?php echo $category['tcat_id']; ?>">
      <div class="section-header">
        <h2 class="section-title"><?php echo htmlspecialchars($category['tcat_name']); ?></h2>
        <?php if(!empty($subcategories)): ?>
          <div class="subcategory-filter">
            <button class="subcategory-btn active" data-subcat-id="all">All</button>
            <?php foreach($subcategories as $subcat): ?>
              <button class="subcategory-btn" data-subcat-id="<?php echo $subcat['mcat_id']; ?>">
                <?php echo htmlspecialchars($subcat['mcat_name']); ?>
              </button>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
      </div>
      <div class="products-grid">
                <?php
        // Fetch products for this category, ensuring we get at least one from each subcategory
        $products = array();

        // Get subcategory IDs
        $subcatIds = array();
        foreach($subcategories as $subcat) {
            $subcatIds[] = $subcat['mcat_id'];
        }

        if (!empty($subcatIds)) {
            // Get one product from each subcategory first
            foreach ($subcatIds as $subcatId) {
                $stmt_prod = $pdo->prepare("SELECT * FROM tbl_product WHERE tcat_id = ? AND mcat_id = ? AND p_is_active = 1 ORDER BY p_id DESC LIMIT 1");
                $stmt_prod->execute(array($category['tcat_id'], $subcatId));
                $product = $stmt_prod->fetch(PDO::FETCH_ASSOC);

                if ($product) {
                    $products[] = $product;
                }
            }

            // If we have fewer than 3 products, get more from any subcategory
            if (count($products) < 3) {
                // Get products we already have
                $existing_ids = array();
                foreach ($products as $product) {
                    $existing_ids[] = $product['p_id'];
                }

                // Prepare the NOT IN clause if we have existing products
                $not_in_clause = '';
                $params = array($category['tcat_id']);

                if (!empty($existing_ids)) {
                    $placeholders = implode(',', array_fill(0, count($existing_ids), '?'));
                    $not_in_clause = " AND p_id NOT IN ($placeholders)";
                    $params = array_merge($params, $existing_ids);
                }

                // Get more products, avoiding duplicates
                $stmt_prod = $pdo->prepare("SELECT * FROM tbl_product WHERE tcat_id = ? $not_in_clause AND p_is_active = 1 ORDER BY p_id DESC LIMIT " . (3 - count($products)));
                $stmt_prod->execute($params);
                $more_products = $stmt_prod->fetchAll(PDO::FETCH_ASSOC);

                // Add more products
                foreach ($more_products as $product) {
                    $products[] = $product;
                }
            }
        } else {
            // If no subcategories, just get 3 products
            $stmt_prod = $pdo->prepare("SELECT * FROM tbl_product WHERE tcat_id = ? AND p_is_active = 1 ORDER BY p_id DESC LIMIT 3");
            $stmt_prod->execute(array($category['tcat_id']));
            $products = $stmt_prod->fetchAll(PDO::FETCH_ASSOC);
        }

        if(!empty($products)):
          foreach($products as $product):
            // Get sub-category ID for this product
            $subcat_id = isset($product['mcat_id']) && !empty($product['mcat_id']) ? $product['mcat_id'] : 'all';
        ?>
            <div class="product-card" data-subcat-id="<?php echo $subcat_id; ?>" data-product-name="<?php echo htmlspecialchars($product['p_name']); ?>" onclick="window.location.href='product_detail.php?id=<?php echo $product['p_id']; ?>'">
              <div class="product-img">
                <img src="../assets/uploads/<?php echo htmlspecialchars($product['p_featured_photo']); ?>" alt="<?php echo htmlspecialchars($product['p_name']); ?>">
              </div>
              <div class="product-content">
                <h3 class="product-title"><?php echo htmlspecialchars($product['p_name']); ?></h3>
                <div class="product-price">Tsh <?php echo htmlspecialchars($product['p_current_price']); ?></div>
                <p>
                  <?php
                  $short_desc = strip_tags($product['p_short_description']);
                  echo (strlen($short_desc) > 100)
                           ? substr($short_desc, 0, 100) . '...'
                           : $short_desc;
                  ?>
                </p>
                <button class="add-to-cart" data-product-id="<?php echo $product['p_id']; ?>" onclick="event.stopPropagation();">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16" style="color: #fff; margin-right: 0.5rem;">
                    <path d="M0 1.5A.5.5 0 0 1 .5 1H2a.5.5 0 0 1 .485.379L2.89 3H14.5a.5.5 0 0 1 .491.592l-1.5 8A.5.5 0 0 1 13 12H4a.5.5 0 0 1-.491-.408L2.01 3.607 1.61 2H.5a.5.5 0 0 1-.5-.5zM3.102 4l1.313 7h8.17l1.313-7H3.102zM5 12a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm7 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm-7 1a1 1 0 1 1 0 2 1 1 0 0 1 0-2zm7 0a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"/>
                  </svg>
                  Add to Cart
                </button>
              </div>
            </div>
                  <?php
                  endforeach;
        else:
                  ?>
          <p>No products found in this category.</p>
        <?php endif; ?>
                </div>
      <div class="view-more">
        <a href="category.php?id=<?php echo $category['tcat_id']; ?>" class="btn view-more-btn">View More &rarr;</a>
      </div>
    </div>
    <?php
      endif; // End of has_products check
    endforeach;
    ?>
  </div>
</section>

  <!-- Best Deals Section -->
<section class="best-deals" id="gallery">
  <div class="container">
    <div class="section-header">
      <h2 class="section-title">🔥 HOT DEALS 🔥</h2>
      <p>Limited-time offers on our most popular smart home devices</p>
    </div>
    <div class="deals-container">
      <?php
      // Fetch 8 cheapest active products with valid discounts
      $stmt_deals = $pdo->prepare("SELECT *,
                                  ((p_old_price - p_current_price) / p_old_price * 100) as discount_percent
                                  FROM tbl_product
                                  WHERE p_is_active = 1
                                  AND p_old_price > p_current_price
                                  AND p_old_price > 0
                                  ORDER BY discount_percent DESC
                                  LIMIT 8");
      $stmt_deals->execute();
      $deals = $stmt_deals->fetchAll(PDO::FETCH_ASSOC);

      foreach($deals as $deal):
        // Calculate discount percentage
        $discount_percent = round(($deal['p_old_price'] - $deal['p_current_price']) / $deal['p_old_price'] * 100);
        $discount = '<span class="discount-badge">🔥 '.$discount_percent.'% OFF</span>';
      ?>
      <div class="deal-card" onclick="window.location.href='product_detail.php?id=<?php echo $deal['p_id']; ?>'" style="cursor: pointer;">
        <?php echo $discount; ?>
        <div class="deal-img">
          <img src="../assets/uploads/<?php echo htmlspecialchars($deal['p_featured_photo']); ?>" alt="<?php echo htmlspecialchars($deal['p_name']); ?>">
        </div>
        <div class="deal-content">
          <h3 class="deal-title"><?php echo htmlspecialchars($deal['p_name']); ?></h3>
          <div class="price-container">
            <?php if($deal['p_old_price'] > 0): ?>
              <span class="old-price">Tsh <?php echo number_format($deal['p_old_price']); ?></span>
              <?php endif; ?>
            <span class="current-price">Tsh <?php echo number_format($deal['p_current_price']); ?></span>
          </div>
          <p class="deal-description"><?php
            $clean_desc = strip_tags($deal['p_short_description']);
            echo htmlspecialchars(mb_substr($clean_desc, 0, 80, 'UTF-8'));
            if (mb_strlen($clean_desc, 'UTF-8') > 80) echo '...';
          ?></p>
          <button class="add-to-cart" data-product-id="<?php echo $deal['p_id']; ?>" onclick="event.stopPropagation();">
            🛒 Add to Cart
          </button>
        </div>
            </div>
          <?php endforeach; ?>
        </div>
    <div class="view-more">
      <a href="deals.php" class="btn view-more-btn">View All Deals 🔥</a>
    </div>
  </div>
</section>
 <!-- Contact Section -->
<section class="contact" id="contact">
  <div class="container">
    <div class="section-header">
      <h2 class="section-title">Get In Touch</h2>
      <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
    </div>
    <div class="contact-container">
      <div class="contact-info">
        <h3>Contact Information</h3>
        <ul class="info-list">
          <li class="info-item">
            <div class="info-icon">📍</div>
            <div class="info-content">
              <h4>Our Location</h4>
              <p>123 Main Street, Suite 100<br />City, State 12345</p>
            </div>
          </li>
          <li class="info-item">
            <div class="info-icon">⏰</div>
            <div class="info-content">
              <h4>Business Hours</h4>
              <p>Monday - Friday: 9AM - 5PM<br />Saturday - Sunday: Closed</p>
            </div>
          </li>
          <li class="info-item">
            <div class="info-icon">📞</div>
            <div class="info-content">
              <h4>Contact Details</h4>
              <p>Phone: +255 (0) 0000000000<br />Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
            </div>
          </li>
        </ul>
          <div class="social-links">
            <a href="#" class="social-link">𝕏</a>
            <a href="#" class="social-link">𝕀</a>
            <a href="#" class="social-link">𝔽</a>
            <a href="#" class="social-link">𝕐</a>
          </div>
        </div>
        <div class="contact-form">
          <h3>Send a Message</h3>
          <div id="message-container"></div>
          <form id="contact-form">
            <div class="form-group">
              <input type="text" class="form-control" name="name" placeholder="Your Name" required />
            </div>
            <div class="form-group">
              <input type="email" class="form-control" name="email" placeholder="Your Email" required />
            </div>
            <div class="form-group">
              <input type="text" class="form-control" name="subject" placeholder="Subject" required />
            </div>
            <div class="form-group">
              <textarea class="form-control" name="message" placeholder="Your Message" required></textarea>
            </div>
            <button type="submit" class="btn">Send Message <span class="btn-icon">→</span></button>
          </form>
        </div>
      </div>
    </div>
  </section>

  <?php include 'includes/footer.php'; ?>

  <!-- Toast Container -->
  <div class="toast-container" id="toastContainer"></div>

  <!-- External JavaScript -->
  <script src="js/script.js"></script>
  <script src="js/subcategory-filter.js"></script>
  <script>
  // Add debugging for subcategory filtering
  console.log('Index page loaded');
  document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded in index.php');

    // Check if subcategory buttons exist
    const subcatButtons = document.querySelectorAll('.subcategory-btn');
    console.log('Found', subcatButtons.length, 'subcategory buttons');

    // Check if product cards exist
    const productCards = document.querySelectorAll('.product-card');
    console.log('Found', productCards.length, 'product cards');

    // Log data-subcat-id attributes for each product card
    productCards.forEach((card, index) => {
      console.log('Product card', index, 'has data-subcat-id:', card.getAttribute('data-subcat-id'));
    });
  });
    document.addEventListener("DOMContentLoaded", function() {
      // Initialize cart with proper validation
      function initializeCart() {
          const cartData = localStorage.getItem('cart');
          let cart = [];

          try {
              cart = cartData ? JSON.parse(cartData) : [];
          } catch (error) {
              console.error('Error parsing existing cart data:', error);
              cart = [];
          }

          // Ensure cart is an array
          if (!Array.isArray(cart)) {
              cart = [];
          }

          // Save clean cart data
          localStorage.setItem('cart', JSON.stringify(cart));
          return cart;
      }

      // Initialize cart and update display
      initializeCart();
      updateCartCount();
      checkCartStatus();

      // Add to cart buttons
      document.querySelectorAll('.add-to-cart').forEach(button => {
          button.addEventListener('click', function(e) {
              e.stopPropagation(); // Prevent any card-level click events
              const productId = this.getAttribute('data-product-id');
              addToCart(productId);
          });
      });

      // Function to check if product is in cart and update button state
      function checkCartStatus() {
          // Get cart with proper error handling
          const cartData = localStorage.getItem('cart');
          let cart = [];

          try {
              cart = cartData ? JSON.parse(cartData) : [];
          } catch (error) {
              console.error('Error parsing cart data in checkCartStatus:', error);
              cart = [];
          }

          // Ensure cart is an array
          if (!Array.isArray(cart)) {
              cart = [];
          }

          document.querySelectorAll('.add-to-cart').forEach(button => {
              const productId = button.getAttribute('data-product-id');
              const isInCart = cart.some(item => String(item.product_id) === productId);

              if (isInCart) {
                  button.disabled = true;
                  button.classList.add('added');
                  button.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Added to Cart
                  `;
              }
          });
      }

      // Updated addToCart function to mimic product_detail.php processing
      function addToCart(productId) {
          const formData = new FormData();
          formData.append('product_id', productId);
          formData.append('quantity', 1);

          // Disable button and show loading state
          const button = document.querySelector(`.add-to-cart[data-product-id="${productId}"]`);
          if (button) {
              button.disabled = true;
              button.innerHTML = `
                <svg class="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Adding...
              `;
          }

          fetch('add_to_cart.php', {
              method: 'POST',
              body: formData
          })
          .then(response => {
              const contentType = response.headers.get("content-type");
              if (response.ok && contentType && contentType.includes("application/json")) {
                  return response.json();
              } else {
                  return response.text().then(text => {
                      throw new Error(`Server error: ${text.substring(0,200)}`);
                  });
              }
          })
          .then(data => {
              if (data && data.status === 'success' && data.added_item) {
                  // Get cart with proper error handling
                  const cartData = localStorage.getItem('cart');
                  let cart = [];

                  try {
                      cart = cartData ? JSON.parse(cartData) : [];
                  } catch (error) {
                      console.error('Error parsing cart data in addToCart:', error);
                      cart = [];
                  }

                  // Ensure cart is an array
                  if (!Array.isArray(cart)) {
                      cart = [];
                  }

                  const productIdStr = String(data.added_item.product_id);
                  const existingIndex = cart.findIndex(item => String(item.product_id) === productIdStr);
                  if (existingIndex > -1) {
                      cart[existingIndex].quantity = data.added_item.quantity;
                  } else {
                      cart.push(data.added_item);
                  }
                  localStorage.setItem('cart', JSON.stringify(cart));
                  updateCartCount();
                  checkCartStatus(); // Update button states after adding to cart
                  showToast('success', 'Product Added!', 'Item has been added to your cart');
              } else {
                  if (button) {
                      button.disabled = false;
                      button.innerHTML = `
                        <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        Add to Cart
                      `;
                  }
                  showToast('error', 'Error!', data.message || 'Error adding product to cart.');
              }
          })
          .catch(error => {
              console.error("Add to Cart Error:", error);
              if (button) {
                  button.disabled = false;
                  button.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    Add to Cart
                  `;
              }
              showToast('error', 'Error!', 'Error adding product to cart.');
          });
      }

      function updateCartCount() {
          // Get cart from localStorage with proper null checking
          const cartData = localStorage.getItem('cart');
          let cart = [];

          try {
              cart = cartData ? JSON.parse(cartData) : [];
          } catch (error) {
              console.error('Error parsing cart data:', error);
              cart = [];
              localStorage.setItem('cart', JSON.stringify([]));
          }

          // Ensure cart is an array
          if (!Array.isArray(cart)) {
              cart = [];
              localStorage.setItem('cart', JSON.stringify([]));
          }

          // Calculate total items with proper validation
          const totalItems = cart.reduce((total, item) => {
              const quantity = parseInt(item.quantity) || 0;
              return total + quantity;
          }, 0);

          // Update all cart count elements (both desktop and mobile)
          document.querySelectorAll('.cart-count').forEach(element => {
              element.textContent = totalItems;
          });

          console.log('Cart updated:', { cart, totalItems }); // Debug log
      }

      // Make updateCartCount globally available for other pages
      window.updateCartCount = updateCartCount;

      // Listen for storage changes to sync cart across tabs
      window.addEventListener('storage', function(e) {
          if (e.key === 'cart') {
              updateCartCount();
              checkCartStatus();
          }
      });

      function showToast(type, title, message) {
          const toastContainer = document.getElementById('toastContainer');
          const toast = document.createElement('div');
          toast.className = `toast ${type}`;

          const icon = type === 'success'
              ? '<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>'
              : '<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>';

          toast.innerHTML = `
              <div class="toast-icon">${icon}</div>
              <div class="toast-content">
                  <div class="toast-title">${title}</div>
                  <div class="toast-message">${message}</div>
              </div>
              <button class="toast-close" onclick="this.parentElement.remove()">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
              </button>
          `;

          toastContainer.appendChild(toast);

          // Trigger animation
          setTimeout(() => toast.classList.add('show'), 10);

          // Auto remove after 3 seconds
          setTimeout(() => {
              toast.classList.remove('show');
              setTimeout(() => toast.remove(), 300);
          }, 3000);
      }
  });

  // Existing slider code remains unchanged
  document.addEventListener("DOMContentLoaded", function() {
      var slider = document.getElementById("bootstrap-touch-slider");
      if (!slider) return;
      var items = slider.querySelectorAll(".item");
      var indicators = slider.querySelectorAll(".carousel-indicators li");
      var current = 0;
      if (items.length <= 1) return;
      function nextSlide() {
          items[current].classList.remove("active");
          if (indicators.length > 0) indicators[current].classList.remove("active");
          current = (current + 1) % items.length;
          items[current].classList.add("active");
          if (indicators.length > 0) indicators[current].classList.add("active");
      }
      var slideInterval = setInterval(nextSlide, 5000);
      indicators.forEach(function(indicator, index) {
          indicator.addEventListener("click", function() {
              clearInterval(slideInterval);
              items[current].classList.remove("active");
              if (indicators.length > 0) indicators[current].classList.remove("active");
              current = index;
              items[current].classList.add("active");
              if (indicators.length > 0) indicators[current].classList.add("active");
              slideInterval = setInterval(nextSlide, 5000);
          });
      });
  });
  </script>

  <script>
  document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const searchSuggestions = document.getElementById('searchSuggestions');
    let searchTimeout;

    // Function to highlight matching text
    function highlightText(text, searchTerm) {
      if (!searchTerm) return text;
      const regex = new RegExp(`(${searchTerm})`, 'gi');
      return text.replace(regex, '<span class="highlight">$1</span>');
    }

    // Function to fetch and display suggestions
    async function fetchSuggestions(searchTerm) {
      if (searchTerm.length < 2) {
        searchSuggestions.classList.remove('active');
        return;
      }

      try {
        const response = await fetch(`search_suggestions.php?q=${encodeURIComponent(searchTerm)}`);
        const data = await response.json();

        if (data.length > 0) {
          searchSuggestions.innerHTML = data.map(item => `
            <div class="suggestion-item" data-type="${item.type}" data-id="${item.id}">
              <div class="icon">${item.type === 'product' ? '🛍️' : '📁'}</div>
              <div class="name">${highlightText(item.name, searchTerm)}</div>
              <div class="type">${item.type}</div>
            </div>
          `).join('');

          searchSuggestions.classList.add('active');
        } else {
          searchSuggestions.innerHTML = '<div class="suggestion-item">No results found</div>';
          searchSuggestions.classList.add('active');
        }
      } catch (error) {
        console.error('Error fetching suggestions:', error);
      }
    }

    // Event listeners
    searchInput.addEventListener('input', function(e) {
      clearTimeout(searchTimeout);
      searchTimeout = setTimeout(() => {
        fetchSuggestions(e.target.value);
      }, 300);
    });

    searchInput.addEventListener('focus', function() {
      if (this.value.length >= 2) {
        fetchSuggestions(this.value);
      }
    });

    document.addEventListener('click', function(e) {
      if (!searchInput.contains(e.target) && !searchSuggestions.contains(e.target)) {
        searchSuggestions.classList.remove('active');
      }
    });

    searchSuggestions.addEventListener('click', function(e) {
      const suggestionItem = e.target.closest('.suggestion-item');
      if (suggestionItem) {
        const type = suggestionItem.dataset.type;
        const id = suggestionItem.dataset.id;

        if (type === 'product') {
          window.location.href = `product_detail.php?id=${id}`;
        } else if (type === 'category') {
          window.location.href = `category.php?id=${id}`;
        }
      }
    });

    // Search button click handler
    const searchButton = document.querySelector('.search-btn');
    const mobileSearchButton = document.getElementById('mobileSearchButton');

    function performSearch() {
      const query = searchInput.value.trim();
      if (query) {
        window.location.href = `search_results.php?q=${encodeURIComponent(query)}`;
      }
    }

    function performMobileSearch() {
      const mobileSearchInput = document.getElementById('mobileSearchInput');
      const query = mobileSearchInput.value.trim();
      if (query) {
        window.location.href = `search_results.php?q=${encodeURIComponent(query)}`;
      }
    }

    searchButton?.addEventListener('click', performSearch);
    mobileSearchButton?.addEventListener('click', performMobileSearch);

    // Enter key handler for search inputs
    searchInput.addEventListener('keypress', function(e) {
      if (e.key === 'Enter') {
        e.preventDefault();
        performSearch();
      }
    });

    const mobileSearchInput = document.getElementById('mobileSearchInput');
    mobileSearchInput?.addEventListener('keypress', function(e) {
      if (e.key === 'Enter') {
        e.preventDefault();
        performMobileSearch();
      }
    });
  });
  </script>
  <script>
  document.addEventListener('DOMContentLoaded', function() {
    const contactForm = document.getElementById('contact-form');
    const messageContainer = document.getElementById('message-container');

    contactForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // Get form data
        const formData = new FormData(contactForm);

        // Show loading state
        const submitButton = contactForm.querySelector('button[type="submit"]');
        const originalButtonText = submitButton.innerHTML;
        submitButton.innerHTML = 'Sending...';
        submitButton.disabled = true;

        // Send form data
        fetch('contact_process.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            // Create message element
            const messageDiv = document.createElement('div');
            messageDiv.className = `alert alert-${data.status === 'success' ? 'success' : 'danger'}`;
            messageDiv.textContent = data.message;

            // Clear previous messages and show new one
            messageContainer.innerHTML = '';
            messageContainer.appendChild(messageDiv);

            // Reset form if successful
            if (data.status === 'success') {
                contactForm.reset();
            }
        })
        .catch(error => {
            // Show error message
            const messageDiv = document.createElement('div');
            messageDiv.className = 'alert alert-danger';
            messageDiv.textContent = 'An error occurred. Please try again.';
            messageContainer.innerHTML = '';
            messageContainer.appendChild(messageDiv);
        })
        .finally(() => {
            // Reset button state
            submitButton.innerHTML = originalButtonText;
            submitButton.disabled = false;
        });
    });
  });
  </script>
  <script>
  document.addEventListener('DOMContentLoaded', function() {
    const newsletterForm = document.getElementById('newsletter-form');
    const newsletterMessage = document.getElementById('newsletter-message');

    newsletterForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // Get form data
        const formData = new FormData(newsletterForm);

        // Show loading state
        const submitButton = newsletterForm.querySelector('button[type="submit"]');
        const originalButtonText = submitButton.innerHTML;
        submitButton.innerHTML = '...';
        submitButton.disabled = true;

        // Send form data
        fetch('subscribe.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            // Create message element
            const messageDiv = document.createElement('div');
            messageDiv.className = `alert alert-${data.status === 'success' ? 'success' : 'danger'}`;
            messageDiv.textContent = data.message;

            // Clear previous messages and show new one
            newsletterMessage.innerHTML = '';
            newsletterMessage.appendChild(messageDiv);

            // Reset form if successful
            if (data.status === 'success') {
                newsletterForm.reset();
            }
        })
        .catch(error => {
            // Show error message
            const messageDiv = document.createElement('div');
            messageDiv.className = 'alert alert-danger';
            messageDiv.textContent = 'An error occurred. Please try again.';
            newsletterMessage.innerHTML = '';
            newsletterMessage.appendChild(messageDiv);
        })
        .finally(() => {
            // Reset button state
            submitButton.innerHTML = originalButtonText;
            submitButton.disabled = false;
        });
    });
  });
  </script>

  <!-- Add this script before the closing </body> tag -->
  <script>
  document.addEventListener('DOMContentLoaded', function() {
    // Get all dropdown containers
    const dropdownContainers = document.querySelectorAll('.dropdown-container');

    dropdownContainers.forEach(container => {
      const toggle = container.querySelector('.dropdown-toggle');
      const menu = container.querySelector('.dropdown-menu');
      const dropdownItems = container.querySelectorAll('.dropdown-item');

      // Toggle main dropdown menu
      toggle.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        // Close all other dropdowns
        dropdownContainers.forEach(otherContainer => {
          if (otherContainer !== container) {
            otherContainer.querySelector('.dropdown-menu').classList.remove('active');
            otherContainer.querySelector('.dropdown-toggle').classList.remove('active');
          }
        });

        // Toggle current dropdown
        menu.classList.toggle('active');
        toggle.classList.toggle('active');
      });

      // Handle subcategory menus
      dropdownItems.forEach(item => {
        const categoryLink = item.querySelector('.category-link');
        const subcategoryMenu = item.querySelector('.subcategory-menu');

        if (subcategoryMenu) {
          categoryLink.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Close all other subcategory menus
            dropdownItems.forEach(otherItem => {
              if (otherItem !== item) {
                const otherSubmenu = otherItem.querySelector('.subcategory-menu');
                if (otherSubmenu) {
                  otherSubmenu.classList.remove('active');
                  otherItem.querySelector('.category-link').classList.remove('active');
                }
              }
            });

            // Toggle current subcategory menu
            subcategoryMenu.classList.toggle('active');
            categoryLink.classList.toggle('active');
          });

          // Add click handler for the category link to navigate
          categoryLink.addEventListener('dblclick', function(e) {
            e.preventDefault();
            e.stopPropagation();
            window.location.href = this.getAttribute('href');
          });
        }
      });
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
      if (!e.target.closest('.dropdown-container')) {
        dropdownContainers.forEach(container => {
          container.querySelector('.dropdown-menu').classList.remove('active');
          container.querySelector('.dropdown-toggle').classList.remove('active');

          // Also close all subcategory menus
          container.querySelectorAll('.subcategory-menu').forEach(menu => {
            menu.classList.remove('active');
          });
          container.querySelectorAll('.category-link').forEach(link => {
            link.classList.remove('active');
          });
        });
      }
    });
  });
  </script>
</body>
</html>
