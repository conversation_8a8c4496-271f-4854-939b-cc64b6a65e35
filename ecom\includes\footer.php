<?php
/**
 * Reusable Footer Component
 * Enhanced modern footer for SMART LIFE e-commerce site
 * 
 * Required variables:
 * - $pdo: Database connection
 * - $footer_copyright: Copyright text (optional, will use default if not set)
 */

// Ensure we have a database connection
if (!isset($pdo)) {
    // If no PDO connection, try to include config
    if (file_exists(__DIR__ . '/../admin/inc/config.php')) {
        include_once(__DIR__ . '/../admin/inc/config.php');
    }
}

// Set default copyright if not provided
if (!isset($footer_copyright) || empty($footer_copyright)) {
    // Try to fetch from database if we have a connection
    if (isset($pdo)) {
        try {
            $stmt = $pdo->prepare("SELECT footer_copyright FROM tbl_settings WHERE id=1");
            $stmt->execute();
            $settings = $stmt->fetch(PDO::FETCH_ASSOC);
            $footer_copyright = $settings['footer_copyright'] ?? "© " . date("Y") . " SMART LIFE. All rights reserved.";
        } catch (PDOException $e) {
            $footer_copyright = "© " . date("Y") . " SMART LIFE. All rights reserved.";
        }
    } else {
        $footer_copyright = "© " . date("Y") . " SMART LIFE. All rights reserved.";
    }
}

// Fetch categories for footer if we have database connection
$footer_categories = [];
if (isset($pdo)) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM tbl_top_category ORDER BY tcat_name ASC LIMIT 6");
        $stmt->execute();
        $footer_categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        // Silently handle error - footer will work without categories
        $footer_categories = [];
    }
}
?>

<!-- Enhanced Modern Footer -->
<footer class="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-gray-300 pt-16 pb-8 mt-20 relative overflow-hidden" style="background: linear-gradient(to bottom right, #111827, #1f2937, #111827) !important; padding: 4rem 0 2rem !important; margin-top: 5rem !important; position: relative !important; overflow: hidden !important; color: #d1d5db !important;">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, #3b82f6 0%, transparent 50%), radial-gradient(circle at 75% 75%, #8b5cf6 0%, transparent 50%);"></div>
    </div>
    
    <div class="container mx-auto px-4 relative z-10" style="max-width: 1200px !important; margin: 0 auto !important; padding: 0 1rem !important; position: relative !important; z-index: 10 !important;">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12 mb-12" style="display: grid !important; grid-template-columns: repeat(1, minmax(0, 1fr)) !important; gap: 2rem !important; margin-bottom: 3rem !important;">
            <!-- Company Info -->
            <div class="lg:col-span-1">
                <div class="mb-6">
                    <h3 class="text-2xl font-bold text-white mb-4 tracking-tight">
                        SMART LIFE<span class="text-blue-400">.</span>
                    </h3>
                    <p class="text-gray-400 text-sm leading-relaxed mb-6">
                        Your gateway to a smarter, more luxurious home. We provide cutting-edge smart home and automation products with exceptional customer service and support.
                    </p>
                </div>
                
                <!-- Social Media Links -->
                <div class="flex space-x-4">
                    <a href="#" class="group bg-gray-800 hover:bg-blue-600 p-3 rounded-full transition-all duration-300 transform hover:scale-110" aria-label="Facebook">
                        <i class="fab fa-facebook-f text-gray-400 group-hover:text-white transition-colors"></i>
                    </a>
                    <a href="#" class="group bg-gray-800 hover:bg-blue-400 p-3 rounded-full transition-all duration-300 transform hover:scale-110" aria-label="Twitter">
                        <i class="fab fa-twitter text-gray-400 group-hover:text-white transition-colors"></i>
                    </a>
                    <a href="#" class="group bg-gray-800 hover:bg-pink-600 p-3 rounded-full transition-all duration-300 transform hover:scale-110" aria-label="Instagram">
                        <i class="fab fa-instagram text-gray-400 group-hover:text-white transition-colors"></i>
                    </a>
                    <a href="#" class="group bg-gray-800 hover:bg-blue-700 p-3 rounded-full transition-all duration-300 transform hover:scale-110" aria-label="LinkedIn">
                        <i class="fab fa-linkedin-in text-gray-400 group-hover:text-white transition-colors"></i>
                    </a>
                </div>
            </div>

            <!-- Quick Links -->
            <div>
                <h4 class="text-lg font-semibold text-white mb-6 relative">
                    Quick Links
                    <div class="absolute bottom-0 left-0 w-8 h-0.5 bg-blue-400 rounded-full"></div>
                </h4>
                <ul class="space-y-3">
                    <li><a href="index.php" class="text-gray-400 hover:text-white hover:translate-x-1 transition-all duration-200 text-sm flex items-center group">
                        <i class="fas fa-home mr-2 text-blue-400 opacity-0 group-hover:opacity-100 transition-opacity"></i>Home
                    </a></li>
                    <li><a href="all_products.php" class="text-gray-400 hover:text-white hover:translate-x-1 transition-all duration-200 text-sm flex items-center group">
                        <i class="fas fa-shopping-bag mr-2 text-blue-400 opacity-0 group-hover:opacity-100 transition-opacity"></i>Shop
                    </a></li>
                    <li><a href="about.php" class="text-gray-400 hover:text-white hover:translate-x-1 transition-all duration-200 text-sm flex items-center group">
                        <i class="fas fa-info-circle mr-2 text-blue-400 opacity-0 group-hover:opacity-100 transition-opacity"></i>About Us
                    </a></li>
                    <li><a href="contact.php" class="text-gray-400 hover:text-white hover:translate-x-1 transition-all duration-200 text-sm flex items-center group">
                        <i class="fas fa-envelope mr-2 text-blue-400 opacity-0 group-hover:opacity-100 transition-opacity"></i>Contact
                    </a></li>
                    <li><a href="subscription_details.php" class="text-gray-400 hover:text-white hover:translate-x-1 transition-all duration-200 text-sm flex items-center group">
                        <i class="fas fa-question-circle mr-2 text-blue-400 opacity-0 group-hover:opacity-100 transition-opacity"></i>FAQs
                    </a></li>
                </ul>
            </div>

            <!-- Categories -->
            <div>
                <h4 class="text-lg font-semibold text-white mb-6 relative">
                    Popular Categories
                    <div class="absolute bottom-0 left-0 w-8 h-0.5 bg-blue-400 rounded-full"></div>
                </h4>
                <ul class="space-y-3">
                    <?php if (!empty($footer_categories)): ?>
                        <?php foreach ($footer_categories as $cat): ?>
                            <li>
                                <a href="category.php?id=<?= $cat['tcat_id'] ?>" 
                                   class="text-gray-400 hover:text-white hover:translate-x-1 transition-all duration-200 text-sm flex items-center group">
                                    <i class="fas fa-tag mr-2 text-blue-400 opacity-0 group-hover:opacity-100 transition-opacity"></i>
                                    <?= htmlspecialchars($cat['tcat_name']) ?>
                                </a>
                            </li>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <li><a href="all_products.php" class="text-gray-400 hover:text-white hover:translate-x-1 transition-all duration-200 text-sm flex items-center group">
                            <i class="fas fa-tag mr-2 text-blue-400 opacity-0 group-hover:opacity-100 transition-opacity"></i>All Products
                        </a></li>
                    <?php endif; ?>
                </ul>
            </div>

            <!-- Contact Info & Newsletter -->
            <div>
                <h4 class="text-lg font-semibold text-white mb-6 relative">
                    Get In Touch
                    <div class="absolute bottom-0 left-0 w-8 h-0.5 bg-blue-400 rounded-full"></div>
                </h4>
                <ul class="space-y-4 mb-6">
                    <li class="flex items-start group">
                        <i class="fas fa-map-marker-alt text-blue-400 mt-1 mr-3 group-hover:scale-110 transition-transform"></i>
                        <span class="text-sm text-gray-400">123 Smart Street, Dar es Salaam, Tanzania</span>
                    </li>
                    <li class="flex items-center group">
                        <i class="fas fa-phone-alt text-blue-400 mr-3 group-hover:scale-110 transition-transform"></i>
                        <a href="tel:+255123456789" class="text-sm text-gray-400 hover:text-white transition-colors">+255 123 456 789</a>
                    </li>
                    <li class="flex items-center group">
                        <i class="fas fa-envelope text-blue-400 mr-3 group-hover:scale-110 transition-transform"></i>
                        <a href="mailto:<EMAIL>" class="text-sm text-gray-400 hover:text-white transition-colors"><EMAIL></a>
                    </li>
                    <li class="flex items-center group">
                        <i class="fas fa-clock text-blue-400 mr-3 group-hover:scale-110 transition-transform"></i>
                        <span class="text-sm text-gray-400">Mon-Sat: 9AM - 6PM</span>
                    </li>
                </ul>

                <!-- Newsletter Signup -->
                <div class="bg-gray-800 bg-opacity-50 p-4 rounded-lg border border-gray-700">
                    <h5 class="text-white font-medium mb-2 text-sm">Stay Updated</h5>
                    <p class="text-xs text-gray-400 mb-3">Get the latest deals and smart home tips</p>
                    <form class="flex" id="footer-newsletter-form">
                        <input type="email" 
                               placeholder="Your email" 
                               class="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-l-md text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent"
                               required>
                        <button type="submit" 
                                class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-r-md transition-colors text-sm font-medium">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Bottom Section -->
        <div class="pt-8 border-t border-gray-700" style="padding-top: 2rem !important; border-top: 1px solid #374151 !important;">
            <div class="flex flex-col lg:flex-row justify-between items-center space-y-4 lg:space-y-0" style="display: flex !important; flex-direction: column !important; justify-content: space-between !important; align-items: center !important; gap: 1rem !important;">
                <!-- Copyright -->
                <div class="text-sm text-center lg:text-left text-gray-400">
                    <?= htmlspecialchars($footer_copyright); ?>
                </div>

                <!-- Links -->
                <div class="flex flex-wrap justify-center lg:justify-end items-center space-x-6 text-sm">
                    <a href="privacy_policy.php" class="text-gray-400 hover:text-white transition-colors">Privacy Policy</a>
                    <a href="shipping_policy.php" class="text-gray-400 hover:text-white transition-colors">Shipping Policy</a>
                    <a href="subscription_details.php" class="text-gray-400 hover:text-white transition-colors">Terms & Conditions</a>
                </div>

                <!-- Payment Methods -->
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-400">Secure Payments:</span>
                    <div class="flex space-x-2">
                        <i class="fab fa-cc-visa text-2xl text-gray-500 hover:text-white transition-colors cursor-pointer" title="Visa"></i>
                        <i class="fab fa-cc-mastercard text-2xl text-gray-500 hover:text-white transition-colors cursor-pointer" title="Mastercard"></i>
                        <i class="fab fa-cc-paypal text-2xl text-gray-500 hover:text-white transition-colors cursor-pointer" title="PayPal"></i>
                        <i class="fab fa-cc-apple-pay text-2xl text-gray-500 hover:text-white transition-colors cursor-pointer" title="Apple Pay"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>

<style>
/* Responsive grid styles for footer - ensures proper layout regardless of page CSS */
@media (min-width: 768px) {
    footer .grid.md\\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
    }
}

@media (min-width: 1024px) {
    footer .grid.lg\\:grid-cols-4 {
        grid-template-columns: repeat(4, minmax(0, 1fr)) !important;
    }

    footer .flex.lg\\:flex-row {
        flex-direction: row !important;
    }

    footer .lg\\:text-left {
        text-align: left !important;
    }

    footer .lg\\:justify-end {
        justify-content: flex-end !important;
    }

    footer .lg\\:gap-12 {
        gap: 3rem !important;
    }
}

/* Ensure all footer text colors are correct */
footer h3, footer h4, footer h5 {
    color: #ffffff !important;
}

footer p, footer span, footer li {
    color: #d1d5db !important;
}

footer a {
    color: #9ca3af !important;
    transition: color 0.2s ease !important;
}

footer a:hover {
    color: #ffffff !important;
}

footer .text-blue-400, footer .text-blue-500 {
    color: #60a5fa !important;
}

footer input {
    color: #ffffff !important;
    background-color: #374151 !important;
}

footer input::placeholder {
    color: #9ca3af !important;
}

footer button {
    background-color: #2563eb !important;
    color: #ffffff !important;
}

footer button:hover {
    background-color: #1d4ed8 !important;
}
</style>

<!-- Back to Top Button -->
<button id="backToTop" 
        class="fixed bottom-8 right-8 bg-blue-600 hover:bg-blue-700 text-white w-12 h-12 rounded-full flex items-center justify-center shadow-lg transform transition-all duration-300 scale-0 hover:scale-110 z-50"
        aria-label="Back to top">
    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
    </svg>
</button>

<script>
// Back to Top Button Functionality
document.addEventListener('DOMContentLoaded', function() {
    const backToTopButton = document.getElementById('backToTop');
    
    if (backToTopButton) {
        // Show/hide button based on scroll position
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                backToTopButton.classList.remove('scale-0');
                backToTopButton.classList.add('scale-100');
            } else {
                backToTopButton.classList.remove('scale-100');
                backToTopButton.classList.add('scale-0');
            }
        });
        
        // Smooth scroll to top
        backToTopButton.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }
    
    // Newsletter form handling
    const newsletterForm = document.getElementById('footer-newsletter-form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const email = this.querySelector('input[type="email"]').value;
            
            // Simple validation
            if (email) {
                // Here you would typically send the email to your backend
                // For now, just show a simple alert
                alert('Thank you for subscribing! We\'ll keep you updated with the latest deals.');
                this.reset();
            }
        });
    }
});
</script>
