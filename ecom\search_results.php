<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

// Log errors to a file as well
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/error.log');

ob_start();
session_start();

// Check if config file exists
if (!file_exists("../admin/inc/config.php")) {
    die("Error: config.php file not found at ../admin/inc/config.php");
}

// Check if functions file exists
if (!file_exists("../admin/inc/functions.php")) {
    die("Error: functions.php file not found at ../admin/inc/functions.php");
}

// Check if CSRF_Protect file exists
if (!file_exists("../admin/inc/CSRF_Protect.php")) {
    die("Error: CSRF_Protect.php file not found at ../admin/inc/CSRF_Protect.php");
}

try {
    include("../admin/inc/config.php");
    include("../admin/inc/functions.php");
    include("../admin/inc/CSRF_Protect.php");
} catch (Exception $e) {
    die("Error including files: " . $e->getMessage());
}

// Get search parameters
$search_query = isset($_GET['q']) ? trim($_GET['q']) : '';
$category_filter = isset($_GET['category']) ? (int)$_GET['category'] : 0;
$sort_by = isset($_GET['sort']) ? $_GET['sort'] : 'relevance';
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$per_page = 12;
$offset = ($page - 1) * $per_page;

// Check database connection
if (!isset($pdo)) {
    die("Error: Database connection not established. Please check your config.php file.");
}

// Fetch settings
try {
    $statement = $pdo->prepare("SELECT * FROM tbl_settings WHERE id=1");
    $statement->execute();
    $settings = $statement->fetch(PDO::FETCH_ASSOC);
    $footer_copyright = $settings['footer_copyright'] ?? "© " . date("Y") . " SMART LIFE. All rights reserved.";
} catch (PDOException $e) {
    die("Database error fetching settings: " . $e->getMessage());
}

// Fetch all categories for filter
try {
    $statement = $pdo->prepare("SELECT * FROM tbl_top_category ORDER BY tcat_name ASC");
    $statement->execute();
    $all_categories = $statement->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    die("Database error fetching categories: " . $e->getMessage());
}

// Initialize search results
$search_results = [];
$total_results = 0;
$search_suggestions = [];

if (!empty($search_query)) {
    // Build search query for products
    $product_search_sql = "
        SELECT p.*, tc.tcat_name, mc.mcat_name,
               CASE
                   WHEN p.p_name LIKE ? THEN 4
                   WHEN p.p_name LIKE ? THEN 3
                   WHEN p.p_short_description LIKE ? THEN 2
                   WHEN p.p_description LIKE ? OR p.p_feature LIKE ? THEN 1
                   ELSE 0
               END as relevance_score
        FROM tbl_product p
        LEFT JOIN tbl_top_category tc ON p.tcat_id = tc.tcat_id
        LEFT JOIN tbl_mid_category mc ON p.mcat_id = mc.mcat_id
        WHERE p.p_is_active = 1
        AND (p.p_name LIKE ? OR p.p_short_description LIKE ? OR p.p_description LIKE ? OR p.p_feature LIKE ?)
    ";

    $search_params = [
        $search_query . '%',  // Exact start match
        '%' . $search_query . '%',  // Contains match
        '%' . $search_query . '%',  // Short description
        '%' . $search_query . '%',  // Description
        '%' . $search_query . '%',  // Features
        '%' . $search_query . '%',  // Name contains
        '%' . $search_query . '%',  // Short description contains
        '%' . $search_query . '%',  // Description contains
        '%' . $search_query . '%'   // Features contains
    ];

    // Add category filter if specified
    if ($category_filter > 0) {
        $product_search_sql .= " AND p.tcat_id = ?";
        $search_params[] = $category_filter;
    }

    // Add sorting
    switch ($sort_by) {
        case 'price_low':
            $product_search_sql .= " ORDER BY relevance_score DESC, CAST(p.p_current_price AS DECIMAL) ASC";
            break;
        case 'price_high':
            $product_search_sql .= " ORDER BY relevance_score DESC, CAST(p.p_current_price AS DECIMAL) DESC";
            break;
        case 'name':
            $product_search_sql .= " ORDER BY relevance_score DESC, p.p_name ASC";
            break;
        case 'newest':
            $product_search_sql .= " ORDER BY relevance_score DESC, p.p_id DESC";
            break;
        default: // relevance
            $product_search_sql .= " ORDER BY relevance_score DESC, p.p_name ASC";
    }

    // Get total count for pagination
    try {
        // Build a simpler count query without the CASE statement
        $count_sql = "
            SELECT COUNT(*) as total
            FROM tbl_product p
            LEFT JOIN tbl_top_category tc ON p.tcat_id = tc.tcat_id
            LEFT JOIN tbl_mid_category mc ON p.mcat_id = mc.mcat_id
            WHERE p.p_is_active = 1
            AND (p.p_name LIKE ? OR p.p_short_description LIKE ? OR p.p_description LIKE ? OR p.p_feature LIKE ?)
        ";

        // Create count parameters (only need the search parameters, not the relevance ones)
        $count_params = [
            '%' . $search_query . '%',  // Name contains
            '%' . $search_query . '%',  // Short description contains
            '%' . $search_query . '%',  // Description contains
            '%' . $search_query . '%'   // Features contains
        ];

        // Add category filter if specified
        if ($category_filter > 0) {
            $count_sql .= " AND p.tcat_id = ?";
            $count_params[] = $category_filter;
        }

        $count_stmt = $pdo->prepare($count_sql);
        $count_stmt->execute($count_params);
        $total_results = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];
    } catch (PDOException $e) {
        die("Database error getting search count: " . $e->getMessage());
    }

    // Get paginated results
    try {
        $product_search_sql .= " LIMIT " . (int)$per_page . " OFFSET " . (int)$offset;

        $stmt = $pdo->prepare($product_search_sql);
        $stmt->execute($search_params);
        $search_results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        die("Database error executing search query: " . $e->getMessage());
    }

    // Get search suggestions for similar terms if no results found
    if (empty($search_results)) {
        try {
            $suggestion_stmt = $pdo->prepare("
                SELECT DISTINCT p_name as suggestion
                FROM tbl_product
                WHERE p_is_active = 1 AND p_name LIKE ?
                LIMIT 5
            ");
            $suggestion_stmt->execute(['%' . substr($search_query, 0, -1) . '%']);
            $search_suggestions = $suggestion_stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            // Don't die here, just log the error and continue without suggestions
            error_log("Database error getting search suggestions: " . $e->getMessage());
            $search_suggestions = [];
        }
    }
}

// Calculate pagination
$total_pages = ceil($total_results / $per_page);
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Search Results<?php echo !empty($search_query) ? ' for "' . htmlspecialchars($search_query) . '"' : ''; ?> | SMART LIFE</title>

  <!-- Debug Information (remove in production) -->
  <?php if (isset($_GET['debug'])): ?>
  <script>
    console.log('Debug Info:');
    console.log('Search Query: <?php echo addslashes($search_query); ?>');
    console.log('Total Results: <?php echo $total_results; ?>');
    console.log('Database Connection: <?php echo isset($pdo) ? "OK" : "FAILED"; ?>');
    console.log('Categories Count: <?php echo count($all_categories); ?>');
  </script>
  <?php endif; ?>
  <link rel="icon" type="image/png" href="../assets/uploads/logo.png">
  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
  <style>
    /* Theme Variables */
    :root {
      --bg-primary: #f8f9fa;
      --bg-secondary: #ffffff;
      --bg-tertiary: #f3f4f6;
      --text-primary: #1f2937;
      --text-secondary: #6b7280;
      --text-tertiary: #9ca3af;
      --border-color: #e5e7eb;
      --shadow-color: rgba(0, 0, 0, 0.1);
    }

    [data-theme="dark"] {
      --bg-primary: #111827;
      --bg-secondary: #1f2937;
      --bg-tertiary: #374151;
      --text-primary: #f9fafb;
      --text-secondary: #d1d5db;
      --text-tertiary: #9ca3af;
      --border-color: #374151;
      --shadow-color: rgba(0, 0, 0, 0.3);
    }

    body {
      background-color: var(--bg-primary);
      color: var(--text-primary);
      transition: background-color 0.3s ease, color 0.3s ease;
    }

    /* Theme toggle button */
    .theme-toggle {
      position: relative;
      width: 60px;
      height: 30px;
      background: var(--bg-tertiary);
      border-radius: 15px;
      border: 1px solid var(--border-color);
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .theme-toggle::before {
      content: '';
      position: absolute;
      top: 2px;
      left: 2px;
      width: 24px;
      height: 24px;
      background: var(--bg-secondary);
      border-radius: 50%;
      transition: all 0.3s ease;
      box-shadow: 0 2px 4px var(--shadow-color);
    }

    [data-theme="dark"] .theme-toggle::before {
      transform: translateX(28px);
    }

    .theme-icon {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      font-size: 12px;
      transition: all 0.3s ease;
    }

    .theme-icon.sun {
      left: 6px;
      color: #fbbf24;
    }

    .theme-icon.moon {
      right: 6px;
      color: #60a5fa;
    }

    [data-theme="dark"] .theme-icon.sun {
      opacity: 0.3;
    }

    [data-theme="light"] .theme-icon.moon {
      opacity: 0.3;
    }

    /* Search highlight */
    .highlight {
      background-color: #fef3c7;
      color: #92400e;
      padding: 1px 2px;
      border-radius: 2px;
      font-weight: 600;
    }

    [data-theme="dark"] .highlight {
      background-color: #451a03;
      color: #fbbf24;
    }

    /* Product card animations */
    .product-card {
      transition: all 0.3s ease;
      background: var(--bg-secondary);
      border: 1px solid var(--border-color);
    }

    .product-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 10px 25px var(--shadow-color);
    }

    /* Filter panel styles */
    .filter-panel {
      background: var(--bg-secondary);
      border: 1px solid var(--border-color);
      border-radius: 12px;
      box-shadow: 0 4px 6px var(--shadow-color);
    }

    /* Pagination styles */
    .pagination-btn {
      background: var(--bg-secondary);
      color: var(--text-primary);
      border: 1px solid var(--border-color);
      transition: all 0.3s ease;
    }

    .pagination-btn:hover {
      background: #4f46e5;
      color: white;
      border-color: #4f46e5;
    }

    .pagination-btn.active {
      background: #4f46e5;
      color: white;
      border-color: #4f46e5;
    }

    /* Loading animation */
    .loading-spinner {
      border: 3px solid var(--border-color);
      border-top: 3px solid #4f46e5;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Empty state illustration */
    .empty-state {
      background: var(--bg-secondary);
      border: 2px dashed var(--border-color);
      border-radius: 12px;
      padding: 3rem;
      text-align: center;
    }

    /* Line clamp utilities */
    .line-clamp-2 {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .line-clamp-3 {
      display: -webkit-box;
      -webkit-line-clamp: 3;
      line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    /* Aspect ratio utilities */
    .aspect-square {
      aspect-ratio: 1 / 1;
    }

    /* Mobile responsive adjustments */
    @media (max-width: 768px) {
      .product-card {
        margin-bottom: 1rem;
      }

      .product-card h3 {
        font-size: 0.875rem;
        line-height: 1.4;
      }

      .product-card p {
        font-size: 0.75rem;
        line-height: 1.3;
      }

      .product-card .p-4 {
        padding: 0.5rem;
      }

      .pagination-btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
      }

      /* Mobile button adjustments */
      .product-card .flex.gap-2 {
        gap: 0.25rem;
      }

      .product-card .flex.gap-2 a,
      .product-card .flex.gap-2 button {
        padding: 0.375rem 0.5rem;
        font-size: 0.75rem;
      }

      /* Mobile price adjustments */
      .product-card .text-xl {
        font-size: 1rem;
      }

      /* Mobile category badge */
      .product-card .text-xs {
        font-size: 0.625rem;
        padding: 0.125rem 0.375rem;
      }

      /* Ensure proper image aspect ratio on mobile */
      .product-card .aspect-square {
        aspect-ratio: 1 / 1;
      }

      .product-card .aspect-square img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    @media (max-width: 480px) {
      .product-card h3 {
        font-size: 0.75rem;
      }

      .product-card p {
        font-size: 0.625rem;
      }

      .product-card .p-4 {
        padding: 0.375rem;
      }

      .product-card .text-xl {
        font-size: 0.875rem;
      }
    }
  </style>
</head>
<body>
  <!-- Debug Status (remove in production) -->
  <?php if (isset($_GET['debug'])): ?>
  <div style="position: fixed; top: 0; left: 0; background: #000; color: #fff; padding: 10px; z-index: 9999; font-size: 12px;">
    DEBUG: Page loaded successfully | DB: <?php echo isset($pdo) ? 'Connected' : 'Failed'; ?> |
    Query: "<?php echo htmlspecialchars($search_query); ?>" |
    Results: <?php echo $total_results; ?>
  </div>
  <?php endif; ?>

  <!-- Theme initialization script -->
  <script>
    (function() {
      const savedTheme = localStorage.getItem('theme');
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      const theme = savedTheme || systemTheme;

      document.documentElement.setAttribute('data-theme', theme);

      window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
        if (!localStorage.getItem('theme')) {
          document.documentElement.setAttribute('data-theme', e.matches ? 'dark' : 'light');
        }
      });
    })();

    function toggleTheme() {
      const currentTheme = document.documentElement.getAttribute('data-theme');
      const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

      document.documentElement.setAttribute('data-theme', newTheme);
      localStorage.setItem('theme', newTheme);
    }
  </script>

  <!-- Header -->
  <header class="fixed inset-x-0 top-0 z-50" style="background: var(--bg-secondary); box-shadow: 0 1px 3px var(--shadow-color);">
    <div class="container mx-auto px-4 flex items-center justify-between py-4">
      <a href="index.php" class="text-2xl font-bold" style="color: var(--text-primary);">
        SMART LIFE<span class="text-blue-600">.</span>
      </a>
      <nav class="hidden md:flex items-center space-x-6">
        <a href="index.php#home" class="hover:text-blue-600 transition" style="color: var(--text-primary);">Home</a>
        <a href="index.php#about" class="hover:text-blue-600 transition" style="color: var(--text-primary);">About</a>
        <a href="index.php#products" class="hover:text-blue-600 transition" style="color: var(--text-primary);">Products</a>
        <a href="index.php#gallery" class="hover:text-blue-600 transition" style="color: var(--text-primary);">Best Deals</a>
        <a href="index.php#contact" class="hover:text-blue-600 transition" style="color: var(--text-primary);">Contact</a>
        <!-- Search -->
        <div class="relative">
          <form action="search_results.php" method="GET" class="flex">
            <input id="searchInput" name="q" type="text" placeholder="Search products, categories..."
                   value="<?php echo htmlspecialchars($search_query); ?>"
                   class="w-64 px-4 py-2 rounded-l-md border focus:outline-none focus:ring-2 focus:ring-[#00c2ff] focus:border-transparent transition-all duration-200"
                   style="background: var(--bg-primary); color: var(--text-primary); border-color: var(--border-color);"
                   autocomplete="off">
            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 transition-colors">
              <i class="fas fa-search"></i>
            </button>
          </form>
          <div id="searchSuggestions"
               class="absolute inset-x-0 mt-1 rounded-lg shadow-xl overflow-hidden hidden z-50 border"
               style="background: var(--bg-secondary); border-color: var(--border-color);"></div>
        </div>
        <!-- Theme Toggle -->
        <div class="theme-toggle" onclick="toggleTheme()" title="Toggle dark/light mode">
          <i class="fas fa-sun theme-icon sun"></i>
          <i class="fas fa-moon theme-icon moon"></i>
        </div>
        <!-- Cart -->
        <a href="cart.php" class="relative text-xl hover:text-blue-600 transition" style="color: var(--text-primary);">
          🛒
          <span class="absolute -top-1 -right-2 bg-blue-600 text-white text-xs rounded-full px-1 cart-count">0</span>
        </a>
      </nav>
      <!-- Mobile Menu Button -->
      <button id="mobileMenuButton" class="md:hidden flex items-center">
        <svg class="w-6 h-6" style="color: var(--text-primary);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16"/>
        </svg>
      </button>
    </div>
  </header>

  <!-- Mobile Menu -->
  <div id="mobileMenu" class="md:hidden fixed right-0 top-0 h-full w-1/2 z-40 transform translate-x-full transition-transform duration-300 ease-in-out shadow-lg" style="background: var(--bg-secondary);">
    <div class="flex flex-col h-full">
      <div class="flex justify-between items-center p-4 border-b" style="border-color: var(--border-color);">
        <a href="index.php" class="text-xl font-bold" style="color: var(--text-primary);">
          SMART LIFE<span class="text-[#00c2ff]">.</span>
        </a>
        <button id="closeMobileMenu" style="color: var(--text-primary);">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>
      <nav class="flex-1 p-4 space-y-4 overflow-y-auto">
        <a href="index.php#home" class="block hover:text-[#00c2ff] transition" style="color: var(--text-primary);">Home</a>
        <a href="index.php#about" class="block hover:text-[#00c2ff] transition" style="color: var(--text-primary);">About</a>
        <a href="index.php#products" class="block hover:text-[#00c2ff] transition" style="color: var(--text-primary);">Products</a>
        <a href="index.php#gallery" class="block hover:text-[#00c2ff] transition" style="color: var(--text-primary);">Best Deals</a>
        <a href="index.php#contact" class="block hover:text-[#00c2ff] transition" style="color: var(--text-primary);">Contact</a>
        <!-- Mobile Search -->
        <div class="relative mt-4">
          <form action="search_results.php" method="GET" class="flex">
            <input id="mobileSearchInput" name="q" type="text" placeholder="Search products, categories..."
                   value="<?php echo htmlspecialchars($search_query); ?>"
                   class="w-full px-4 py-2 rounded-l-md border focus:outline-none focus:ring-2 focus:ring-[#00c2ff] focus:border-transparent transition-all duration-200"
                   style="background: var(--bg-primary); color: var(--text-primary); border-color: var(--border-color);"
                   autocomplete="off">
            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 transition-colors">
              <i class="fas fa-search"></i>
            </button>
          </form>
        </div>
        <!-- Cart in Mobile Menu -->
        <a href="cart.php" class="flex items-center hover:text-[#00c2ff] transition" style="color: var(--text-primary);">
          <span class="text-xl mr-2">🛒</span>
          <span class="bg-[#00c2ff] text-white text-xs rounded-full px-2 py-1 cart-count">0</span>
        </a>
      </nav>
    </div>
  </div>
  <!-- Backdrop for mobile menu -->
  <div id="mobileMenuBackdrop" class="md:hidden fixed inset-0 bg-black bg-opacity-50 z-30 hidden"></div>

  <main class="pt-24 pb-12">
    <section class="container mx-auto px-4">
      <!-- Search Header -->
      <div class="mb-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 class="text-3xl font-bold mb-2" style="color: var(--text-primary);">
              <?php if (!empty($search_query)): ?>
                Search Results for "<span class="text-blue-600"><?php echo htmlspecialchars($search_query); ?></span>"
              <?php else: ?>
                Search Products
              <?php endif; ?>
            </h1>
            <?php if (!empty($search_query)): ?>
              <p style="color: var(--text-secondary);">
                Found <?php echo $total_results; ?> result<?php echo $total_results !== 1 ? 's' : ''; ?>
                <?php if ($category_filter > 0): ?>
                  in selected category
                <?php endif; ?>
              </p>
            <?php endif; ?>
          </div>

          <?php if (!empty($search_query)): ?>
            <!-- Sort and Filter Controls -->
            <div class="flex flex-col sm:flex-row gap-3">
              <!-- Category Filter -->
              <select id="categoryFilter" onchange="updateFilters()"
                      class="px-4 py-2 rounded-md border focus:outline-none focus:ring-2 focus:ring-blue-500"
                      style="background: var(--bg-secondary); color: var(--text-primary); border-color: var(--border-color);">
                <option value="0">All Categories</option>
                <?php foreach ($all_categories as $category): ?>
                  <option value="<?php echo $category['tcat_id']; ?>"
                          <?php echo $category_filter == $category['tcat_id'] ? 'selected' : ''; ?>>
                    <?php echo htmlspecialchars($category['tcat_name']); ?>
                  </option>
                <?php endforeach; ?>
              </select>

              <!-- Sort Options -->
              <select id="sortBy" onchange="updateFilters()"
                      class="px-4 py-2 rounded-md border focus:outline-none focus:ring-2 focus:ring-blue-500"
                      style="background: var(--bg-secondary); color: var(--text-primary); border-color: var(--border-color);">
                <option value="relevance" <?php echo $sort_by === 'relevance' ? 'selected' : ''; ?>>Most Relevant</option>
                <option value="name" <?php echo $sort_by === 'name' ? 'selected' : ''; ?>>Name A-Z</option>
                <option value="price_low" <?php echo $sort_by === 'price_low' ? 'selected' : ''; ?>>Price: Low to High</option>
                <option value="price_high" <?php echo $sort_by === 'price_high' ? 'selected' : ''; ?>>Price: High to Low</option>
                <option value="newest" <?php echo $sort_by === 'newest' ? 'selected' : ''; ?>>Newest First</option>
              </select>

              <!-- Clear Filters -->
              <?php if ($category_filter > 0 || $sort_by !== 'relevance'): ?>
                <a href="search_results.php?q=<?php echo urlencode($search_query); ?>"
                   class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors text-center">
                  Clear Filters
                </a>
              <?php endif; ?>
            </div>
          <?php endif; ?>
        </div>
      </div>

      <!-- Search Results Content -->
      <?php if (empty($search_query)): ?>
        <!-- Empty Search State -->
        <div class="empty-state">
          <div class="text-6xl mb-4">🔍</div>
          <h2 class="text-2xl font-bold mb-2" style="color: var(--text-primary);">Start Your Search</h2>
          <p style="color: var(--text-secondary);">Enter a product name or category to find what you're looking for</p>
        </div>
      <?php elseif (empty($search_results)): ?>
        <!-- No Results State -->
        <div class="empty-state">
          <div class="text-6xl mb-4">😔</div>
          <h2 class="text-2xl font-bold mb-2" style="color: var(--text-primary);">No Results Found</h2>
          <p class="mb-4" style="color: var(--text-secondary);">
            We couldn't find any products matching "<strong><?php echo htmlspecialchars($search_query); ?></strong>"
          </p>

          <?php if (!empty($search_suggestions)): ?>
            <div class="mt-6">
              <h3 class="text-lg font-semibold mb-3" style="color: var(--text-primary);">Did you mean:</h3>
              <div class="flex flex-wrap gap-2 justify-center">
                <?php foreach ($search_suggestions as $suggestion): ?>
                  <a href="search_results.php?q=<?php echo urlencode($suggestion['suggestion']); ?>"
                     class="px-4 py-2 bg-blue-100 text-blue-800 rounded-full hover:bg-blue-200 transition-colors">
                    <?php echo htmlspecialchars($suggestion['suggestion']); ?>
                  </a>
                <?php endforeach; ?>
              </div>
            </div>
          <?php endif; ?>

          <div class="mt-6">
            <h3 class="text-lg font-semibold mb-3" style="color: var(--text-primary);">Search Tips:</h3>
            <ul class="text-left space-y-1" style="color: var(--text-secondary);">
              <li>• Check your spelling</li>
              <li>• Try more general keywords</li>
              <li>• Use fewer keywords</li>
              <li>• Browse our <a href="all_products.php" class="text-blue-600 hover:underline">all products</a> page</li>
            </ul>
          </div>
        </div>
      <?php else: ?>
        <!-- Search Results Grid -->
        <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4 lg:gap-6 mb-8">
          <?php
          function highlightSearchTerm($text, $search_term) {
            if (empty($search_term)) return htmlspecialchars(strip_tags($text));
            $clean_text = strip_tags($text); // Remove HTML tags first
            $highlighted = preg_replace('/(' . preg_quote($search_term, '/') . ')/i', '<span class="highlight">$1</span>', htmlspecialchars($clean_text));
            return $highlighted;
          }

          foreach ($search_results as $product):
          ?>
            <div class="product-card rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300">
              <!-- Product Image -->
              <div class="relative aspect-square overflow-hidden">
                <img src="../assets/uploads/<?php echo htmlspecialchars($product['p_featured_photo']); ?>"
                     alt="<?php echo htmlspecialchars($product['p_name']); ?>"
                     class="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                     loading="lazy">

                <!-- Relevance Badge for Top Results -->
                <?php if ($product['relevance_score'] >= 3): ?>
                  <div class="absolute top-2 left-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full font-semibold">
                    Best Match
                  </div>
                <?php endif; ?>

                <!-- Stock Status -->
                <?php if ($product['p_qty'] <= 0): ?>
                  <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                    <span class="text-white font-semibold">Out of Stock</span>
                  </div>
                <?php endif; ?>
              </div>

              <!-- Product Info -->
              <div class="p-4">
                <div class="mb-2">
                  <span class="text-xs px-2 py-1 rounded-full"
                        style="background: var(--bg-tertiary); color: var(--text-secondary);">
                    <?php echo htmlspecialchars($product['tcat_name']); ?>
                  </span>
                </div>

                <h3 class="font-semibold text-lg mb-2 line-clamp-2" style="color: var(--text-primary);">
                  <?php echo highlightSearchTerm($product['p_name'], $search_query); ?>
                </h3>

                <p class="text-sm mb-3 line-clamp-2" style="color: var(--text-secondary);">
                  <?php echo highlightSearchTerm($product['p_short_description'], $search_query); ?>
                </p>

                <!-- Price -->
                <div class="flex items-center justify-between mb-4">
                  <div class="flex items-center space-x-2">
                    <span class="text-xl font-bold text-blue-600">
                      TSh <?php echo number_format($product['p_current_price']); ?>
                    </span>
                    <?php if ($product['p_old_price'] > $product['p_current_price']): ?>
                      <span class="text-sm line-through" style="color: var(--text-tertiary);">
                        TSh <?php echo number_format($product['p_old_price']); ?>
                      </span>
                    <?php endif; ?>
                  </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex gap-2">
                  <a href="product_detail.php?id=<?php echo $product['p_id']; ?>"
                     class="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors text-center font-medium">
                    View Details
                  </a>
                  <?php if ($product['p_qty'] > 0): ?>
                    <button class="bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors add-to-cart"
                            data-product-id="<?php echo $product['p_id']; ?>">
                      <i class="fas fa-cart-plus"></i>
                    </button>
                  <?php endif; ?>
                </div>
              </div>
            </div>
          <?php endforeach; ?>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
          <div class="flex justify-center items-center space-x-2 mt-8">
            <!-- Previous Button -->
            <?php if ($page > 1): ?>
              <a href="?q=<?php echo urlencode($search_query); ?>&category=<?php echo $category_filter; ?>&sort=<?php echo $sort_by; ?>&page=<?php echo $page - 1; ?>"
                 class="pagination-btn px-4 py-2 rounded-lg">
                <i class="fas fa-chevron-left"></i> Previous
              </a>
            <?php endif; ?>

            <!-- Page Numbers -->
            <?php
            $start_page = max(1, $page - 2);
            $end_page = min($total_pages, $page + 2);

            if ($start_page > 1): ?>
              <a href="?q=<?php echo urlencode($search_query); ?>&category=<?php echo $category_filter; ?>&sort=<?php echo $sort_by; ?>&page=1"
                 class="pagination-btn px-3 py-2 rounded-lg">1</a>
              <?php if ($start_page > 2): ?>
                <span class="px-2" style="color: var(--text-secondary);">...</span>
              <?php endif; ?>
            <?php endif; ?>

            <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
              <a href="?q=<?php echo urlencode($search_query); ?>&category=<?php echo $category_filter; ?>&sort=<?php echo $sort_by; ?>&page=<?php echo $i; ?>"
                 class="pagination-btn px-3 py-2 rounded-lg <?php echo $i === $page ? 'active' : ''; ?>">
                <?php echo $i; ?>
              </a>
            <?php endfor; ?>

            <?php if ($end_page < $total_pages): ?>
              <?php if ($end_page < $total_pages - 1): ?>
                <span class="px-2" style="color: var(--text-secondary);">...</span>
              <?php endif; ?>
              <a href="?q=<?php echo urlencode($search_query); ?>&category=<?php echo $category_filter; ?>&sort=<?php echo $sort_by; ?>&page=<?php echo $total_pages; ?>"
                 class="pagination-btn px-3 py-2 rounded-lg"><?php echo $total_pages; ?></a>
            <?php endif; ?>

            <!-- Next Button -->
            <?php if ($page < $total_pages): ?>
              <a href="?q=<?php echo urlencode($search_query); ?>&category=<?php echo $category_filter; ?>&sort=<?php echo $sort_by; ?>&page=<?php echo $page + 1; ?>"
                 class="pagination-btn px-4 py-2 rounded-lg">
                Next <i class="fas fa-chevron-right"></i>
              </a>
            <?php endif; ?>
          </div>
        <?php endif; ?>
      <?php endif; ?>
    </section>
  </main>

  <!-- Footer -->
  <footer class="py-12" style="background: var(--bg-secondary); border-top: 1px solid var(--border-color);">
    <div class="container mx-auto px-4">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <!-- Company Info -->
        <div class="md:col-span-2">
          <h3 class="text-2xl font-bold mb-4" style="color: var(--text-primary);">
            SMART LIFE<span class="text-blue-600">.</span>
          </h3>
          <p class="mb-4" style="color: var(--text-secondary);">
            Your trusted partner for smart home solutions. We provide cutting-edge technology
            to make your life easier and more efficient.
          </p>
          <div class="flex space-x-4">
            <a href="#" class="text-blue-600 hover:text-blue-700 transition-colors">
              <i class="fab fa-facebook-f text-xl"></i>
            </a>
            <a href="#" class="text-blue-600 hover:text-blue-700 transition-colors">
              <i class="fab fa-twitter text-xl"></i>
            </a>
            <a href="#" class="text-blue-600 hover:text-blue-700 transition-colors">
              <i class="fab fa-instagram text-xl"></i>
            </a>
            <a href="#" class="text-blue-600 hover:text-blue-700 transition-colors">
              <i class="fab fa-linkedin-in text-xl"></i>
            </a>
          </div>
        </div>

        <!-- Quick Links -->
        <div>
          <h4 class="font-semibold mb-4" style="color: var(--text-primary);">Quick Links</h4>
          <ul class="space-y-2">
            <li><a href="index.php#home" class="hover:text-blue-600 transition-colors" style="color: var(--text-secondary);">Home</a></li>
            <li><a href="index.php#about" class="hover:text-blue-600 transition-colors" style="color: var(--text-secondary);">About</a></li>
            <li><a href="all_products.php" class="hover:text-blue-600 transition-colors" style="color: var(--text-secondary);">Products</a></li>
            <li><a href="index.php#contact" class="hover:text-blue-600 transition-colors" style="color: var(--text-secondary);">Contact</a></li>
          </ul>
        </div>

        <!-- Contact Info -->
        <div>
          <h4 class="font-semibold mb-4" style="color: var(--text-primary);">Contact Info</h4>
          <ul class="space-y-2" style="color: var(--text-secondary);">
            <li class="flex items-center">
              <i class="fas fa-phone mr-2 text-blue-600"></i>
              +255 123 456 789
            </li>
            <li class="flex items-center">
              <i class="fas fa-envelope mr-2 text-blue-600"></i>
              <EMAIL>
            </li>
            <li class="flex items-center">
              <i class="fas fa-map-marker-alt mr-2 text-blue-600"></i>
              Dar es Salaam, Tanzania
            </li>
          </ul>
        </div>
      </div>

      <div class="border-t mt-8 pt-8 text-center" style="border-color: var(--border-color);">
        <p style="color: var(--text-secondary);"><?php echo $footer_copyright; ?></p>
      </div>
    </div>
  </footer>

  <!-- JavaScript -->
  <script>
    // Mobile menu functionality
    const mobileMenuButton = document.getElementById('mobileMenuButton');
    const mobileMenu = document.getElementById('mobileMenu');
    const closeMobileMenu = document.getElementById('closeMobileMenu');
    const mobileMenuBackdrop = document.getElementById('mobileMenuBackdrop');

    function openMobileMenu() {
      mobileMenu.classList.remove('translate-x-full');
      mobileMenuBackdrop.classList.remove('hidden');
      document.body.style.overflow = 'hidden';
    }

    function closeMobileMenuFunc() {
      mobileMenu.classList.add('translate-x-full');
      mobileMenuBackdrop.classList.add('hidden');
      document.body.style.overflow = '';
    }

    mobileMenuButton?.addEventListener('click', openMobileMenu);
    closeMobileMenu?.addEventListener('click', closeMobileMenuFunc);
    mobileMenuBackdrop?.addEventListener('click', closeMobileMenuFunc);

    // Filter update functionality
    function updateFilters() {
      const searchQuery = '<?php echo addslashes($search_query); ?>';
      const category = document.getElementById('categoryFilter').value;
      const sort = document.getElementById('sortBy').value;

      const url = new URL(window.location.href);
      url.searchParams.set('q', searchQuery);
      url.searchParams.set('category', category);
      url.searchParams.set('sort', sort);
      url.searchParams.set('page', '1'); // Reset to first page

      window.location.href = url.toString();
    }

    // Function to check if product is in cart and update button state
    function checkCartStatus() {
      const cart = JSON.parse(localStorage.getItem('cart') || '[]');
      document.querySelectorAll('.add-to-cart').forEach(button => {
        const productId = button.getAttribute('data-product-id');
        const isInCart = cart.some(item => String(item.product_id) === productId);

        if (isInCart) {
          button.disabled = true;
          button.classList.add('opacity-50', 'cursor-not-allowed');
          button.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            Added to Cart
          `;
        }
      });
    }

    // Updated addToCart function to mimic product_detail.php processing
    function addToCart(productId) {
      const formData = new FormData();
      formData.append('product_id', productId);
      formData.append('quantity', 1);

      fetch('add_to_cart.php', {
        method: 'POST',
        body: formData
      })
      .then(response => {
        const contentType = response.headers.get("content-type");
        if (response.ok && contentType && contentType.includes("application/json")) {
          return response.json();
        } else {
          return response.text().then(text => {
            throw new Error(`Server error: ${text.substring(0,200)}`);
          });
        }
      })
      .then(data => {
        if (data && data.status === 'success' && data.added_item) {
          let cart = JSON.parse(localStorage.getItem('cart') || '[]');
          const productIdStr = String(data.added_item.product_id);
          const existingIndex = cart.findIndex(item => String(item.product_id) === productIdStr);
          if (existingIndex > -1) {
            cart[existingIndex].quantity = data.added_item.quantity;
          } else {
            cart.push(data.added_item);
          }
          localStorage.setItem('cart', JSON.stringify(cart));
          updateCartCount();
          checkCartStatus(); // Update button states after adding to cart
          showToast();
        } else {
          showToast('error', data.message || 'Error adding product to cart.');
        }
      })
      .catch(error => {
        console.error("Add to Cart Error:", error);
        showToast('error', 'Error adding product to cart.');
      });
    }

    function updateCartCount() {
      const cart = JSON.parse(localStorage.getItem('cart') || '[]'); // Ensure cart is parsed correctly
      const totalItems = cart.reduce((total, item) => total + (item.quantity || 0), 0); // Sum quantities

      // Update header cart count
      const headerCartCount = document.querySelector('header .cart-count');
      if(headerCartCount) headerCartCount.textContent = totalItems;

      // Update mobile menu cart count
      const mobileCartCount = document.querySelector('#mobileMenu .cart-count');
      if(mobileCartCount) mobileCartCount.textContent = totalItems;
    }

    function showToast(type = 'success', message = 'Product added to cart!') {
      // Remove existing toast
      const existingToast = document.querySelector('.toast');
      if (existingToast) {
        existingToast.remove();
      }

      // Create toast element
      const toast = document.createElement('div');
      toast.className = `toast fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;

      // Set toast color based on type
      if (type === 'success') {
        toast.classList.add('bg-green-500', 'text-white');
      } else if (type === 'error') {
        toast.classList.add('bg-red-500', 'text-white');
      } else {
        toast.classList.add('bg-blue-500', 'text-white');
      }

      toast.innerHTML = `
        <div class="flex items-center">
          <span>${message}</span>
          <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
            <i class="fas fa-times"></i>
          </button>
        </div>
      `;

      document.body.appendChild(toast);

      // Show toast
      setTimeout(() => {
        toast.classList.remove('translate-x-full');
      }, 100);

      // Auto hide after 3 seconds
      setTimeout(() => {
        toast.classList.add('translate-x-full');
        setTimeout(() => {
          toast.remove();
        }, 300);
      }, 3000);
    }

    // Search suggestions functionality
    let searchTimeout;
    const searchInput = document.getElementById('searchInput');
    const searchSuggestions = document.getElementById('searchSuggestions');

    function highlightText(text, searchTerm) {
      if (!searchTerm) return text;
      const regex = new RegExp(`(${searchTerm})`, 'gi');
      return text.replace(regex, '<span class="highlight">$1</span>');
    }

    async function fetchSuggestions(searchTerm) {
      if (searchTerm.length < 2) {
        searchSuggestions.classList.add('hidden');
        return;
      }

      try {
        const response = await fetch(`search_suggestions.php?q=${encodeURIComponent(searchTerm)}`);
        const data = await response.json();

        if (data.length > 0) {
          searchSuggestions.innerHTML = data.map(item => `
            <div class="suggestion-item flex items-center px-4 py-3 hover:bg-gray-50 cursor-pointer transition-colors duration-150 border-b border-gray-100 last:border-b-0"
                 onclick="selectSuggestion('${item.type}', '${item.id}', '${item.name.replace(/'/g, "\\'")}')">
              <div class="icon mr-3 text-[#00c2ff]">${item.type === 'product' ? '🛍️' : '📁'}</div>
              <div class="name flex-1 text-gray-700">${highlightText(item.name, searchTerm)}</div>
              <div class="ml-2 text-xs text-gray-400">${item.type}</div>
            </div>
          `).join('');
          searchSuggestions.classList.remove('hidden');
        } else {
          searchSuggestions.innerHTML = '<div class="px-4 py-3 text-gray-500">No suggestions found</div>';
          searchSuggestions.classList.remove('hidden');
        }
      } catch (error) {
        console.error('Error fetching suggestions:', error);
      }
    }

    function selectSuggestion(type, id, name) {
      if (type === 'product') {
        window.location.href = `product_detail.php?id=${id}`;
      } else if (type === 'category') {
        window.location.href = `category.php?id=${id}`;
      }
    }

    // Search input event listeners
    searchInput?.addEventListener('input', function(e) {
      clearTimeout(searchTimeout);
      searchTimeout = setTimeout(() => {
        fetchSuggestions(e.target.value);
      }, 300);
    });

    searchInput?.addEventListener('focus', function() {
      if (this.value.length >= 2) {
        fetchSuggestions(this.value);
      }
    });

    // Hide suggestions when clicking outside
    document.addEventListener('click', function(e) {
      if (!searchInput?.contains(e.target) && !searchSuggestions?.contains(e.target)) {
        searchSuggestions?.classList.add('hidden');
      }
    });

    // Initialize cart count on page load
    document.addEventListener('DOMContentLoaded', function() {
      // Initialize cart if not exists
      if (!localStorage.getItem('cart')) {
        localStorage.setItem('cart', JSON.stringify([]));
      }

      // Update cart count display and check product status
      updateCartCount();
      checkCartStatus();

      // Add to cart buttons
      document.querySelectorAll('.add-to-cart').forEach(button => {
        button.addEventListener('click', function(e) {
          e.stopPropagation(); // Prevent any card-level click events

          // Don't proceed if button is disabled
          if (this.disabled) {
            return;
          }

          const productId = this.getAttribute('data-product-id');
          addToCart(productId);
        });
      });
    });
  </script>
</body>
</html>
